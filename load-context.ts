import type { Request as CloudflareRequest } from "@cloudflare/workers-types";
import type { PlatformProxy } from "wrangler";
import { type Database, createDb } from "./app/lib/db";

let dbInstance: Database | null = null;

type GetLoadContextArgs = {
  request: CloudflareRequest;
  context: {
    cloudflare: Omit<PlatformProxy<Env>, "dispose" | "caches" | "cf"> & {
      caches: PlatformProxy<Env>["caches"] | CacheStorage;
      cf: Request["cf"];
    };
  };
};

declare module "@remix-run/cloudflare" {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface AppLoadContext extends ReturnType<typeof getLoadContext> {
    // This will merge the result of `getLoadContext` into the `AppLoadContext`
  }
}

export function getLoadContext({ context }: GetLoadContextArgs) {
  // Initialize database
  if (!dbInstance) {
    const databaseUrl = context.cloudflare.env.DATABASE_URL;
    if (!databaseUrl) {
      // In development, provide a warning instead of throwing an error
      console.warn(
        "⚠️  DATABASE_URL environment variable is not set. Database functionality will be disabled."
      );
      console.warn(
        "   To enable database features, add DATABASE_URL to your .env file or environment variables."
      );
      dbInstance = null;
    } else {
      dbInstance = createDb(databaseUrl);
    }
  }

  return {
    ...context,
    db: dbInstance,
  };
}
