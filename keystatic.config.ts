import { collection, config, fields, singleton } from "@keystatic/core";

export default config({
  storage: {
    kind: "local",
  },
  collections: {
    posts: collection({
      label: "Blog Posts",
      slugField: "title",
      path: "content/posts/*",
      format: { contentField: "content" },
      schema: {
        title: fields.slug({ name: { label: "Title" } }),
        publishedDate: fields.date({
          label: "Published Date",
          defaultValue: { kind: "today" },
        }),
        author: fields.text({
          label: "Author",
          defaultValue: "Admin",
        }),
        excerpt: fields.text({
          label: "Excerpt",
          description: "A brief summary of the post",
          multiline: true,
        }),
        featuredImage: fields.image({
          label: "Featured Image",
          directory: "public/images/blog",
          publicPath: "/images/blog/",
        }),
        tags: fields.array(fields.text({ label: "Tag" }), {
          label: "Tags",
          itemLabel: (props) => props.value,
        }),
        category: fields.select({
          label: "Category",
          options: [
            { label: "AI & Technology", value: "ai-technology" },
            { label: "Tutorials", value: "tutorials" },
            { label: "News & Updates", value: "news" },
            { label: "Best Practices", value: "best-practices" },
            { label: "Case Studies", value: "case-studies" },
          ],
          defaultValue: "ai-technology",
        }),
        status: fields.select({
          label: "Status",
          options: [
            { label: "Draft", value: "draft" },
            { label: "Published", value: "published" },
            { label: "Archived", value: "archived" },
          ],
          defaultValue: "draft",
        }),
        seo: fields.object({
          title: fields.text({
            label: "SEO Title",
            description: "Override the default title for SEO",
          }),
          description: fields.text({
            label: "SEO Description",
            description: "Meta description for search engines",
            multiline: true,
          }),
          keywords: fields.array(fields.text({ label: "Keyword" }), {
            label: "SEO Keywords",
            itemLabel: (props) => props.value,
          }),
        }),
        content: fields.document({
          label: "Content",
          formatting: true,
          dividers: true,
          links: true,
          images: {
            directory: "public/images/blog",
            publicPath: "/images/blog/",
          },
        }),
      },
    }),
    pages: collection({
      label: "Pages",
      slugField: "title",
      path: "content/pages/*",
      format: { contentField: "content" },
      schema: {
        title: fields.slug({ name: { label: "Title" } }),
        description: fields.text({
          label: "Description",
          multiline: true,
        }),
        slug: fields.text({
          label: "URL Slug",
          description: 'The URL path for this page (e.g., "about-us")',
        }),
        status: fields.select({
          label: "Status",
          options: [
            { label: "Draft", value: "draft" },
            { label: "Published", value: "published" },
          ],
          defaultValue: "draft",
        }),
        seo: fields.object({
          title: fields.text({
            label: "SEO Title",
          }),
          description: fields.text({
            label: "SEO Description",
            multiline: true,
          }),
        }),
        content: fields.document({
          label: "Content",
          formatting: true,
          dividers: true,
          links: true,
          images: {
            directory: "public/images/pages",
            publicPath: "/images/pages/",
          },
        }),
      },
    }),
    testimonials: collection({
      label: "Testimonials",
      slugField: "name",
      path: "content/testimonials/*",
      schema: {
        name: fields.slug({ name: { label: "Customer Name" } }),
        company: fields.text({
          label: "Company",
        }),
        position: fields.text({
          label: "Position/Title",
        }),
        avatar: fields.image({
          label: "Avatar",
          directory: "public/images/testimonials",
          publicPath: "/images/testimonials/",
        }),
        rating: fields.select({
          label: "Rating",
          options: [
            { label: "5 Stars", value: "5" },
            { label: "4 Stars", value: "4" },
            { label: "3 Stars", value: "3" },
            { label: "2 Stars", value: "2" },
            { label: "1 Star", value: "1" },
          ],
          defaultValue: "5",
        }),
        testimonial: fields.text({
          label: "Testimonial",
          multiline: true,
        }),
        featured: fields.checkbox({
          label: "Featured",
          description: "Show this testimonial on the homepage",
        }),
      },
    }),
    faqs: collection({
      label: "FAQs",
      slugField: "question",
      path: "content/faqs/*",
      schema: {
        question: fields.slug({ name: { label: "Question" } }),
        answer: fields.text({
          label: "Answer",
          multiline: true,
        }),
        category: fields.select({
          label: "Category",
          options: [
            { label: "General", value: "general" },
            { label: "Pricing", value: "pricing" },
            { label: "Technical", value: "technical" },
            { label: "AI Features", value: "ai-features" },
            { label: "Account", value: "account" },
          ],
          defaultValue: "general",
        }),
        order: fields.integer({
          label: "Display Order",
          defaultValue: 0,
        }),
      },
    }),
  },
  singletons: {
    settings: singleton({
      label: "Site Settings",
      path: "content/settings",
      schema: {
        siteName: fields.text({
          label: "Site Name",
          defaultValue: "AI SaaS Starter",
        }),
        siteDescription: fields.text({
          label: "Site Description",
          multiline: true,
          defaultValue: "A modern AI-powered SaaS platform built with Remix and Cloudflare.",
        }),
        siteUrl: fields.url({
          label: "Site URL",
          defaultValue: "https://your-domain.com",
        }),
        logo: fields.image({
          label: "Logo",
          directory: "public/images",
          publicPath: "/images/",
        }),
        favicon: fields.image({
          label: "Favicon",
          directory: "public",
          publicPath: "/",
        }),
        socialMedia: fields.object({
          twitter: fields.url({ label: "Twitter URL" }),
          github: fields.url({ label: "GitHub URL" }),
          linkedin: fields.url({ label: "LinkedIn URL" }),
          discord: fields.url({ label: "Discord URL" }),
        }),
        analytics: fields.object({
          googleAnalyticsId: fields.text({
            label: "Google Analytics ID",
            description: "GA4 Measurement ID (e.g., G-XXXXXXXXXX)",
          }),
          googleTagManagerId: fields.text({
            label: "Google Tag Manager ID",
            description: "GTM Container ID (e.g., GTM-XXXXXXX)",
          }),
        }),
        seo: fields.object({
          defaultTitle: fields.text({
            label: "Default SEO Title",
            defaultValue: "AI SaaS Starter - Modern AI Platform",
          }),
          defaultDescription: fields.text({
            label: "Default SEO Description",
            multiline: true,
            defaultValue: "Build powerful AI applications with our comprehensive SaaS platform.",
          }),
          defaultImage: fields.image({
            label: "Default Social Share Image",
            directory: "public/images",
            publicPath: "/images/",
          }),
        }),
      },
    }),
    homepage: singleton({
      label: "Homepage Content",
      path: "content/homepage",
      schema: {
        hero: fields.object({
          title: fields.text({
            label: "Hero Title",
            defaultValue: "Build the Future with AI",
          }),
          subtitle: fields.text({
            label: "Hero Subtitle",
            multiline: true,
            defaultValue: "Powerful AI tools and APIs to accelerate your development.",
          }),
          ctaText: fields.text({
            label: "CTA Button Text",
            defaultValue: "Get Started Free",
          }),
          ctaUrl: fields.text({
            label: "CTA Button URL",
            defaultValue: "/signup",
          }),
          backgroundImage: fields.image({
            label: "Hero Background Image",
            directory: "public/images",
            publicPath: "/images/",
          }),
        }),
        features: fields.array(
          fields.object({
            title: fields.text({ label: "Feature Title" }),
            description: fields.text({
              label: "Feature Description",
              multiline: true,
            }),
            icon: fields.text({
              label: "Icon Name",
              description: 'Lucide icon name (e.g., "zap", "brain", "rocket")',
            }),
          }),
          {
            label: "Features",
            itemLabel: (props) => props.fields.title.value,
          }
        ),
        stats: fields.array(
          fields.object({
            label: fields.text({ label: "Stat Label" }),
            value: fields.text({ label: "Stat Value" }),
            description: fields.text({ label: "Stat Description" }),
          }),
          {
            label: "Statistics",
            itemLabel: (props) => props.fields.label.value,
          }
        ),
      },
    }),
  },
});
