# AI Tools Setup Guide

This guide will help you set up and configure the AI tools integration in your Remix + Cloudflare application.

## Overview

The AI tools integration supports multiple AI providers:

- **OpenAI** - GPT models, DALL-E image generation
- **DeepSeek** - DeepSeek chat and reasoning models
- **OpenRouter** - Access to multiple AI models through one API
- **SiliconFlow** - Chinese AI model provider
- **Replicate** - Open source AI models
- **Cloudflare Workers AI** - Edge AI models (no API key required)

## Environment Variables Setup

### 1. Cloudflare Workers AI Setup

Cloudflare Workers AI is automatically available when you deploy to Cloudflare Workers. No API key is required!

To enable Cloudflare Workers AI, add the AI binding to your `wrangler.toml`:

```toml
[ai]
binding = "AI"
```

And update your TypeScript definitions in `worker-configuration.d.ts`:

```typescript
interface Env {
  // ... other bindings
  AI: Ai;
}
```

### 2. Local Development (.dev.vars)

Create a `.dev.vars` file in your project root with your API keys:

```bash
# Database
DATABASE_URL="your-neon-database-url"

# AI Provider API Keys
OPENAI_API_KEY="sk-your-openai-api-key"
DEEPSEEK_API_KEY="your-deepseek-api-key"
OPENROUTER_API_KEY="sk-or-your-openrouter-key"
SILICONFLOW_API_KEY="your-siliconflow-api-key"
SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"
REPLICATE_API_TOKEN="r8_your-replicate-token"

# Other services
STRIPE_PUBLIC_KEY="pk_test_your-stripe-public-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
GA_TRACKING_ID="G-XXXXXXXXXX"
GOOGLE_CLIENT_ID="your-google-client-id"
ONE_TAP_ENABLED="true"
```

### 2. Production Deployment (Cloudflare)

For production, add these environment variables to your Cloudflare Workers:

```bash
# Using Wrangler CLI
wrangler secret put OPENAI_API_KEY
wrangler secret put DEEPSEEK_API_KEY
wrangler secret put OPENROUTER_API_KEY
wrangler secret put SILICONFLOW_API_KEY
wrangler secret put REPLICATE_API_TOKEN
wrangler secret put DATABASE_URL
wrangler secret put STRIPE_SECRET_KEY

# Or through Cloudflare Dashboard:
# Go to Workers & Pages > Your Worker > Settings > Environment Variables
```

## Getting API Keys

### OpenAI
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up/login and go to API Keys
3. Create a new secret key
4. Add billing information to use the API

**Supported Models:**
- `gpt-4o` - Latest GPT-4 model
- `gpt-4o-mini` - Faster, cheaper GPT-4
- `gpt-4-turbo` - Previous generation
- `gpt-3.5-turbo` - Fast and affordable
- `dall-e-2`, `dall-e-3` - Image generation

### DeepSeek
1. Visit [DeepSeek Platform](https://platform.deepseek.com/)
2. Register and get your API key
3. Very cost-effective for coding tasks

**Supported Models:**
- `deepseek-chat` - General conversation
- `deepseek-coder` - Code generation
- `deepseek-r1` - Reasoning model

### OpenRouter
1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up and get your API key
3. Access to 100+ AI models through one API

**Popular Models:**
- `deepseek/deepseek-r1` - Reasoning model
- `anthropic/claude-3.5-sonnet` - Claude model
- `openai/gpt-4o` - GPT-4 through OpenRouter
- `google/gemini-pro-1.5` - Google's model

### SiliconFlow (Chinese Provider)
1. Visit [SiliconFlow](https://siliconflow.cn/)
2. Register and get your API key
3. Good for Chinese users, competitive pricing

**Supported Models:**
- `deepseek-ai/DeepSeek-R1` - Reasoning model
- `Qwen/Qwen2.5-72B-Instruct` - Alibaba's model
- `meta-llama/Meta-Llama-3.1-405B-Instruct` - Meta's model

### Replicate
1. Visit [Replicate](https://replicate.com/)
2. Sign up and get your API token
3. Access to open source AI models

**Supported Models:**
- `meta/llama-2-70b-chat` - Meta's Llama
- `mistralai/mixtral-8x7b-instruct-v0.1` - Mistral model

### Cloudflare Workers AI
No setup required! Cloudflare Workers AI is automatically available when deployed to Cloudflare Workers.

**Key Benefits:**
- ✅ **No API key required** - Uses Cloudflare AI binding
- ✅ **Edge computing** - Models run close to your users
- ✅ **Cost-effective** - Competitive pricing with generous free tier
- ✅ **Multiple capabilities** - Text, embeddings, classification, speech-to-text

**Supported Models:**
- `@cf/meta/llama-3.2-3b-instruct` - Fast text generation
- `@cf/meta/llama-3-8b-instruct` - Balanced performance
- `@cf/meta/llama-guard-3-8b` - Content moderation
- `@cf/baai/bge-m3` - Text embeddings
- `@cf/huggingface/distilbert-sst-2-int8` - Sentiment analysis
- `@cf/microsoft/resnet-50` - Image classification
- `@cf/openai/whisper` - Speech-to-text

**Available Capabilities:**
- **Text Generation** - Chat and completion models
- **Text Embeddings** - Vector representations for semantic search
- **Text Classification** - Sentiment analysis and categorization
- **Image Classification** - Object and scene recognition
- **Speech Recognition** - Audio to text conversion

## Usage Examples

### Text Generation
```typescript
const response = await fetch("/api/ai/generate-text", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    prompt: "Write a hello world program in Python",
    provider: "openai",
    model: "gpt-4o-mini"
  })
});
```

### Stream Text
```typescript
const response = await fetch("/api/ai/stream-text", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    prompt: "Explain quantum computing",
    provider: "deepseek",
    model: "deepseek-chat"
  })
});

// Handle streaming response
const reader = response.body?.getReader();
// ... process stream
```

### Image Generation
```typescript
const response = await fetch("/api/ai/generate-image", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    prompt: "A beautiful sunset over mountains",
    provider: "openai",
    model: "dall-e-3",
    size: "1024x1024"
  })
});
```

### Cloudflare AI Examples

#### Text Generation
```typescript
const formData = new FormData();
formData.append("action", "generate-text");
formData.append("model", "llama-3.2-3b");
formData.append("prompt", "Explain quantum computing in simple terms");

const response = await fetch("/api/ai/cloudflare", {
  method: "POST",
  body: formData
});
```

#### Text Embeddings
```typescript
const formData = new FormData();
formData.append("action", "generate-embeddings");
formData.append("model", "bge-m3");
formData.append("text", "This is a sample text for embedding");

const response = await fetch("/api/ai/cloudflare", {
  method: "POST",
  body: formData
});
```

#### Text Classification (Sentiment Analysis)
```typescript
const formData = new FormData();
formData.append("action", "classify-text");
formData.append("model", "distilbert-sst-2");
formData.append("text", "I love this product! It's amazing!");

const response = await fetch("/api/ai/cloudflare", {
  method: "POST",
  body: formData
});
```

#### Image Classification
```typescript
const formData = new FormData();
formData.append("action", "classify-image");
formData.append("model", "resnet-50");
formData.append("image", imageFile); // File object

const response = await fetch("/api/ai/cloudflare", {
  method: "POST",
  body: formData
});
```

## Testing the Integration

1. **Start Development Server:**
   ```bash
   yarn dev
   ```

2. **Visit AI Tools Page:**
   Navigate to `/ai-tools` in your browser

3. **Test Each Provider:**
   - Select a provider that you have API keys for
   - Choose a model
   - Enter a prompt
   - Test text generation, streaming, and image generation

## Cost Management

### Credit System
The application includes a credit system to manage AI usage:

- **Text Generation:** 5 credits per request
- **Stream Text:** 3 credits per request  
- **Image Generation:** 10 credits per image

### Provider Costs (Approximate)
- **OpenAI GPT-4o:** $15/1M input tokens, $60/1M output tokens
- **OpenAI GPT-4o-mini:** $0.15/1M input tokens, $0.60/1M output tokens
- **DeepSeek:** $0.14/1M input tokens, $0.28/1M output tokens
- **OpenRouter:** Varies by model, usually competitive
- **SiliconFlow:** Very competitive pricing for Chinese users
- **Cloudflare Workers AI:** Very competitive with generous free tier, pay-per-use pricing

## Troubleshooting

### Common Issues

1. **"API key is missing" Error:**
   - Check your `.dev.vars` file has the correct API key
   - Ensure the environment variable name matches exactly
   - Restart your development server after adding keys

2. **"Rate limit exceeded" Error:**
   - You've hit the API rate limit
   - Wait a few minutes or upgrade your API plan
   - Consider using a different provider

3. **"Model not found" Error:**
   - Check the model name is correct for the provider
   - Some models may not be available in your region
   - Try a different model from the same provider

4. **Streaming not working:**
   - Ensure your browser supports streaming
   - Check network connectivity
   - Some corporate firewalls block streaming responses

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
```

This will show detailed logs for AI operations in the console.

## Security Best Practices

1. **Never commit API keys to version control**
2. **Use environment variables for all sensitive data**
3. **Implement rate limiting in production**
4. **Monitor API usage and costs**
5. **Use least-privilege API keys when possible**
6. **Regularly rotate API keys**

## Next Steps

1. **Implement user authentication** to track usage per user
2. **Add more AI providers** as needed
3. **Implement caching** to reduce API costs
4. **Add usage analytics** to monitor performance
5. **Create custom AI workflows** for your specific use cases
