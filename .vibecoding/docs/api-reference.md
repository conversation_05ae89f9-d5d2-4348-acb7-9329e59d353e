# API Documentation

This document describes the API routes that have been created for your Remix application, adapted from the Next.js project structure.

## Overview

The APIs have been converted from Next.js App Router format (`app/api/*/route.ts`) to Remix format (`app/routes/api.*.tsx`). Each API route exports an `action` function that handles HTTP requests.

## API Routes

### 1. Ping API
**Route:** `/api/ping`  
**Method:** POST  
**Purpose:** Test API connectivity and consume user credits

**Request Body:**
```json
{
  "message": "Hello World"
}
```

**Response:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "pong": "received message: Hello World"
  }
}
```

### 2. Get User Info API
**Route:** `/api/get-user-info`  
**Method:** POST  
**Purpose:** Retrieve current user information including credits

**Response:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "uuid": "user-uuid",
    "name": "User Name",
    "email": "<EMAIL>",
    "credits": 100,
    "invite_code": "ABC123"
  }
}
```

### 3. Get User Credits API
**Route:** `/api/get-user-credits`  
**Method:** POST  
**Purpose:** Get user's current credit balance

**Response:**
```json
{
  "code": 0,
  "message": "success",
  "data": 150
}
```

### 4. Add Feedback API
**Route:** `/api/add-feedback`  
**Method:** POST  
**Purpose:** Submit user feedback

**Request Body:**
```json
{
  "content": "Great service!",
  "rating": 5
}
```

**Response:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 123,
    "content": "Great service!",
    "rating": 5,
    "status": "created",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### 5. Update Invite Code API
**Route:** `/api/update-invite-code`  
**Method:** POST  
**Purpose:** Set or update user's invite code

**Request Body:**
```json
{
  "invite_code": "MYCODE123"
}
```

### 6. Update Invite API
**Route:** `/api/update-invite`  
**Method:** POST  
**Purpose:** Use someone else's invite code to establish referral relationship

**Request Body:**
```json
{
  "invite_code": "FRIEND123"
}
```

### 7. Checkout API
**Route:** `/api/checkout`  
**Method:** POST  
**Purpose:** Create Stripe checkout session for payments

**Request Body:**
```json
{
  "product_id": "basic_monthly",
  "product_name": "Basic Monthly Plan",
  "amount": 999,
  "currency": "usd",
  "interval": "month",
  "credits": 100,
  "valid_months": 1,
  "cancel_url": "https://yoursite.com/cancel"
}
```

### 8. Stripe Notify API
**Route:** `/api/stripe-notify`  
**Method:** POST  
**Purpose:** Handle Stripe webhook notifications (for Stripe use only)

## Response Format

All APIs use a consistent response format:

```json
{
  "code": 0,        // 0 = success, negative = error
  "message": "success",
  "data": {}        // Response data (optional)
}
```

Error codes:
- `0`: Success
- `-1`: General error
- `-2`: Authentication error
- `-500`: Server error

## Authentication

Currently, the APIs use mock authentication. You need to implement real authentication by updating the `getUserUuid()` and `getUserEmail()` functions in `app/services/user.ts`.

## Database Integration

The APIs currently use mock data. You need to implement real database operations in:
- `app/models/user.ts`
- `app/models/feedback.ts`
- `app/models/order.ts`

## Environment Variables

Add these to your `.dev.vars` file for local development:

```
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
WEB_URL=http://localhost:3000
PROJECT_NAME=Your Project Name
```

## Testing

Visit `/api-demo` to test all the APIs with a simple UI.

## Next Steps

1. **Implement Authentication**: Replace mock authentication with real auth system
2. **Database Integration**: Connect to your Neon database
3. **Error Handling**: Add proper error handling and validation
4. **Rate Limiting**: Add rate limiting for production use
5. **Logging**: Add proper logging and monitoring
6. **Testing**: Add unit and integration tests

## Differences from Next.js

| Next.js | Remix |
|---------|-------|
| `app/api/ping/route.ts` | `app/routes/api.ping.tsx` |
| `export async function POST(req)` | `export async function action({ request })` |
| `return Response.json(data)` | `return json(data)` |
| `process.env.VAR` | `context.cloudflare.env.VAR` |
| Direct request handling | Remix action function pattern |
