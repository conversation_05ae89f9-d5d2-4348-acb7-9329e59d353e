# Authentication System Documentation

## Overview

This project implements a comprehensive authentication system using:
- **Google One Tap** for seamless user sign-in
- **JWT tokens** for session management
- **Neon PostgreSQL** database for user and session storage
- **Remix + Cloudflare Workers** for server-side authentication
- **Zustand** for client-side state management

## Architecture

```
Frontend (Browser)
├── Google One Tap SDK
├── Zustand Auth Store
└── Protected Routes

Auth Worker (/auth/google)
├── Google ID Token Verification
├── User Creation/Update
├── JWT Token Generation
└── Session Management

Database (Neon PostgreSQL)
├── users table
├── sessions table
└── accounts table
```

## Setup Instructions

### 1. Environment Variables

Copy `.dev.vars.example` to `.dev.vars` and configure:

```bash
# Database
DATABASE_URL="****************************************************************"

# Google Authentication
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
ONE_TAP_ENABLED="true"

# JWT Secret (minimum 32 characters)
JWT_SECRET="your-super-secret-jwt-key-change-in-production-min-32-chars"
```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add your domain to authorized origins
6. Copy the Client ID to your environment variables

### 3. Database Migration

Run the migration to add the sessions table:

```bash
# Using your preferred database client, run:
psql $DATABASE_URL -f app/lib/db/migrations/add-sessions-table.sql

# Or using Drizzle (if configured):
yarn db:push
```

### 4. Install Dependencies

The required dependencies are already included in package.json:
- `google-one-tap` - Google One Tap SDK
- `jose` - JWT handling
- `zustand` - State management

## Usage

### Protected Routes

Wrap your routes with authentication:

```tsx
// Server-side protection (recommended)
export async function loader({ request, context }: LoaderFunctionArgs) {
  const user = await requireAuth(request, context.cloudflare?.env);
  return json({ user });
}

// Client-side protection (additional layer)
export default function ProtectedPage() {
  return (
    <ProtectedRoute>
      <YourPageContent />
    </ProtectedRoute>
  );
}
```

### Using Auth State

```tsx
import { useAuth } from '~/components/auth/auth-provider';

function MyComponent() {
  const { user, isAuthenticated, logout } = useAuth();
  
  if (!isAuthenticated) {
    return <div>Please sign in</div>;
  }
  
  return (
    <div>
      <p>Welcome, {user.name}!</p>
      <p>Credits: {user.credits}</p>
      <button onClick={logout}>Sign Out</button>
    </div>
  );
}
```

### User Menu Component

```tsx
import { UserMenu } from '~/components/auth/user-menu';

function Header() {
  return (
    <header>
      <nav>
        {/* Other nav items */}
        <UserMenu />
      </nav>
    </header>
  );
}
```

## API Endpoints

### Authentication Routes

- `POST /auth/google` - Google One Tap authentication
- `GET|POST /auth/logout` - User logout
- `GET /api/auth/me` - Get current user info

### Usage Examples

```javascript
// Sign in with Google One Tap
const response = await fetch('/auth/google', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ credential: googleIdToken }),
});

// Get current user
const user = await fetch('/api/auth/me');

// Logout
await fetch('/auth/logout', { method: 'POST' });
```

## Security Features

### JWT Tokens
- Access tokens expire in 7 days
- Refresh tokens expire in 30 days
- Tokens are stored in HTTP-only cookies
- CSRF protection with SameSite cookies

### Session Management
- Database-backed sessions
- Automatic cleanup of expired sessions
- IP address and user agent tracking
- Session invalidation on logout

### Google Authentication
- ID token verification with Google's API
- Email verification requirement
- Secure token exchange

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  uuid UUID UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  avatar TEXT,
  credits INTEGER DEFAULT 0,
  signin_type VARCHAR(50),
  signin_provider VARCHAR(50),
  signin_openid TEXT,
  signin_ip VARCHAR(45),
  -- ... other fields
);
```

### Sessions Table
```sql
CREATE TABLE sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  session_token VARCHAR(255) UNIQUE NOT NULL,
  refresh_token VARCHAR(255) UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  is_active BOOLEAN DEFAULT true,
  -- ... other fields
);
```

## Troubleshooting

### Common Issues

1. **Google One Tap not showing**
   - Check GOOGLE_CLIENT_ID is correct
   - Verify domain is authorized in Google Console
   - Ensure HTTPS in production

2. **JWT verification fails**
   - Check JWT_SECRET is set and consistent
   - Verify token hasn't expired
   - Check cookie settings

3. **Database connection issues**
   - Verify DATABASE_URL format
   - Check Neon database is accessible
   - Run migrations

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
```

## Production Considerations

1. **Security**
   - Use strong JWT_SECRET (32+ characters)
   - Enable HTTPS
   - Set secure cookie flags
   - Regular session cleanup

2. **Performance**
   - Database connection pooling
   - Session cleanup job
   - Token refresh strategy

3. **Monitoring**
   - Failed authentication attempts
   - Session duration analytics
   - User activity tracking

## Contributing

When adding new authentication features:
1. Update this documentation
2. Add appropriate tests
3. Consider security implications
4. Update TypeScript types
