# 极简认证系统文档

## 概述

这是一个极简的认证系统，专为 Remix + Cloudflare Workers + Neon 架构设计：
- **Google One Tap** 优先认证（90% 用户一键登录）
- **Neon Auth** 兜底认证（邮箱登录备选）
- **统一 JWT 验证器** 处理两种来源的 Token
- **Postgres RLS** 作为最终安全防线
- **Zustand** 客户端状态管理

## 极简架构

```
浏览器
├── Google One Tap (自动弹窗)
├── Neon Auth 按钮 (备选)
└── Zustand Store (状态)

Cloudflare Worker
├── verifyJwt() (统一验证器)
├── requireUser() (认证中间件)
└── Cookie 管理

Neon Database
├── users 表
└── RLS 策略 (auth.user_id() = user_id)
```

## Setup Instructions

### 1. Environment Variables

Copy `.dev.vars.example` to `.dev.vars` and configure:

```bash
# Database
DATABASE_URL="****************************************************************"

# Google Authentication
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
ONE_TAP_ENABLED="true"

# JWT Secret (minimum 32 characters)
JWT_SECRET="your-super-secret-jwt-key-change-in-production-min-32-chars"
```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add your domain to authorized origins
6. Copy the Client ID to your environment variables

### 3. Database Migration

Run the migration to add the sessions table:

```bash
# Using your preferred database client, run:
psql $DATABASE_URL -f app/lib/db/migrations/add-sessions-table.sql

# Or using Drizzle (if configured):
yarn db:push
```

### 4. Install Dependencies

The required dependencies are already included in package.json:
- `google-one-tap` - Google One Tap SDK
- `jose` - JWT handling
- `zustand` - State management

## 使用方法

### 服务端路由保护

```tsx
import { requireUser } from "~/lib/auth/middleware.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // 这是唯一需要的认证函数
  const user = await requireUser(request);

  // user 包含 { id, email, name, avatar, credits, jwt }
  // jwt 可用于数据库查询的 authToken
  return json({ user });
}
```

### 客户端状态管理

```tsx
import { useAuthStore } from "~/stores/auth-store";

function MyComponent() {
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const logout = useAuthStore((state) => state.logout);

  if (!isAuthenticated) {
    return <div>请登录</div>;
  }

  return (
    <div>
      <p>欢迎, {user.name}!</p>
      <p>积分: {user.credits}</p>
      <button onClick={logout}>登出</button>
    </div>
  );
}
```

### 数据库查询（带 RLS）

```tsx
import { neon } from "@neondatabase/serverless";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);

  // 使用 JWT 作为 authToken，启用 RLS
  const db = neon(process.env.DATABASE_URL!, { authToken: user.jwt });

  // 查询会自动过滤为当前用户的数据
  const userPosts = await db`SELECT * FROM posts WHERE user_id = auth.user_id()`;

  return json({ posts: userPosts });
}
```

## API Endpoints

### Authentication Routes

- `POST /auth/google` - Google One Tap authentication
- `GET|POST /auth/logout` - User logout
- `GET /api/auth/me` - Get current user info

### Usage Examples

```javascript
// Sign in with Google One Tap
const response = await fetch('/auth/google', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ credential: googleIdToken }),
});

// Get current user
const user = await fetch('/api/auth/me');

// Logout
await fetch('/auth/logout', { method: 'POST' });
```

## Security Features

### JWT Tokens
- Access tokens expire in 7 days
- Refresh tokens expire in 30 days
- Tokens are stored in HTTP-only cookies
- CSRF protection with SameSite cookies

### Session Management
- Database-backed sessions
- Automatic cleanup of expired sessions
- IP address and user agent tracking
- Session invalidation on logout

### Google Authentication
- ID token verification with Google's API
- Email verification requirement
- Secure token exchange

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  uuid UUID UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  avatar TEXT,
  credits INTEGER DEFAULT 0,
  signin_type VARCHAR(50),
  signin_provider VARCHAR(50),
  signin_openid TEXT,
  signin_ip VARCHAR(45),
  -- ... other fields
);
```

### Sessions Table
```sql
CREATE TABLE sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  session_token VARCHAR(255) UNIQUE NOT NULL,
  refresh_token VARCHAR(255) UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  is_active BOOLEAN DEFAULT true,
  -- ... other fields
);
```

## Troubleshooting

### Common Issues

1. **Google One Tap not showing**
   - Check GOOGLE_CLIENT_ID is correct
   - Verify domain is authorized in Google Console
   - Ensure HTTPS in production

2. **JWT verification fails**
   - Check JWT_SECRET is set and consistent
   - Verify token hasn't expired
   - Check cookie settings

3. **Database connection issues**
   - Verify DATABASE_URL format
   - Check Neon database is accessible
   - Run migrations

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
```

## Production Considerations

1. **Security**
   - Use strong JWT_SECRET (32+ characters)
   - Enable HTTPS
   - Set secure cookie flags
   - Regular session cleanup

2. **Performance**
   - Database connection pooling
   - Session cleanup job
   - Token refresh strategy

3. **Monitoring**
   - Failed authentication attempts
   - Session duration analytics
   - User activity tracking

## Contributing

When adding new authentication features:
1. Update this documentation
2. Add appropriate tests
3. Consider security implications
4. Update TypeScript types
