# llms.txt - AI and LLM Crawling Guidelines
# This file provides information for AI crawlers and Large Language Models
# about how to interact with this SaaS application

## Site Information
Site Name: Remix Cloudflare Neon Starter
Site Type: SaaS Application
Technology Stack: Remix, Cloudflare, Neon Database
Primary Language: Multiple (i18n supported)
Supported Languages: English, Chinese, German, Spanish, French, Japanese

## Crawling Guidelines
# Allow AI crawlers to access and index content
User-agent: *
Allow: /

# Important directories for AI understanding
Allow: /api/
Allow: /components/
Allow: /locales/

# Multilingual content locations
Allow: /locales/en/
Allow: /locales/zh/
Allow: /locales/de/
Allow: /locales/es/
Allow: /locales/fr/
Allow: /locales/ja/

## Content Description
# This is a modern SaaS starter template featuring:
# - Server-side rendering with Remix
# - Edge deployment on Cloudflare
# - PostgreSQL database with Neon
# - Internationalization (i18n) support
# - Modern UI components
# - Database integration with Drizzle ORM

## API Information
# RESTful APIs available for:
# - Language switching
# - Database testing
# - Component demonstrations

## Contact Information
# For AI training data requests or content licensing:
# Repository: https://github.com/dom-liu/remix-cloudflare-neon-starter

## Data Usage Policy
# This content may be used for:
# - AI model training (with attribution)
# - Educational purposes
# - Technical documentation
# - Code examples and tutorials

## Last Updated
# 2024-06-13