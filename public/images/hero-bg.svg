<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e2e8f0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cbd5e1;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="circleGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0.05" />
    </linearGradient>
    
    <linearGradient id="circleGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:0.05" />
    </linearGradient>
    
    <linearGradient id="circleGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:0.05" />
    </linearGradient>
  </defs>
  
  <rect width="1200" height="600" fill="url(#bgGradient)"/>
  
  <!-- Floating geometric shapes -->
  <!-- Large circles -->
  <circle cx="200" cy="150" r="80" fill="url(#circleGradient1)" opacity="0.6">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 20,10; 0,0" dur="8s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="900" cy="100" r="60" fill="url(#circleGradient2)" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; -15,20; 0,0" dur="10s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="1000" cy="400" r="100" fill="url(#circleGradient3)" opacity="0.4">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 25,-15; 0,0" dur="12s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Medium circles -->
  <circle cx="100" cy="400" r="40" fill="url(#circleGradient2)" opacity="0.3">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 10,15; 0,0" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="1100" cy="200" r="50" fill="url(#circleGradient1)" opacity="0.4">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; -20,10; 0,0" dur="9s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Small circles -->
  <circle cx="300" cy="500" r="25" fill="url(#circleGradient3)" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 8,12; 0,0" dur="7s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="800" cy="500" r="30" fill="url(#circleGradient1)" opacity="0.3">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 12,-8; 0,0" dur="11s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Hexagons -->
  <polygon points="500,80 520,90 520,110 500,120 480,110 480,90" fill="url(#circleGradient2)" opacity="0.4">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 500 100; 360 500 100" dur="20s" repeatCount="indefinite"/>
  </polygon>
  
  <polygon points="700,300 730,315 730,345 700,360 670,345 670,315" fill="url(#circleGradient1)" opacity="0.3">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 700 330; -360 700 330" dur="25s" repeatCount="indefinite"/>
  </polygon>
  
  <!-- Triangles -->
  <polygon points="150,300 170,270 190,300" fill="url(#circleGradient3)" opacity="0.4">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 5,10; 0,0" dur="8s" repeatCount="indefinite"/>
  </polygon>
  
  <polygon points="950,450 980,420 1010,450" fill="url(#circleGradient2)" opacity="0.3">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; -8,5; 0,0" dur="9s" repeatCount="indefinite"/>
  </polygon>
  
  <!-- Grid pattern overlay -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="1200" height="600" fill="url(#grid)"/>
  
  <!-- Subtle tech elements -->
  <!-- Code brackets -->
  <text x="50" y="50" fill="#64748b" font-family="monospace" font-size="24" opacity="0.2">&lt;/&gt;</text>
  <text x="1100" y="550" fill="#64748b" font-family="monospace" font-size="20" opacity="0.2">{}</text>
  
  <!-- Binary pattern -->
  <text x="400" y="40" fill="#94a3b8" font-family="monospace" font-size="12" opacity="0.15">01001001</text>
  <text x="600" y="580" fill="#94a3b8" font-family="monospace" font-size="12" opacity="0.15">11010110</text>
  
  <!-- Connecting lines -->
  <path d="M200 150 Q400 200 600 180" stroke="#3b82f6" stroke-width="1" fill="none" opacity="0.2">
    <animate attributeName="stroke-dasharray" values="0,1000; 1000,0" dur="15s" repeatCount="indefinite"/>
  </path>
  
  <path d="M900 100 Q700 250 500 300" stroke="#10b981" stroke-width="1" fill="none" opacity="0.2">
    <animate attributeName="stroke-dasharray" values="0,800; 800,0" dur="12s" repeatCount="indefinite"/>
  </path>
</svg>
