<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#8b5cf6"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#aiGradient)" opacity="0.1"/>
  
  <!-- Brain/AI symbol -->
  <path d="M16 18 Q20 14 24 18 Q28 14 32 18 Q34 20 32 24 Q34 28 32 30 Q28 34 24 30 Q20 34 16 30 Q14 28 16 24 Q14 20 16 18 Z" 
        fill="url(#aiGradient)" opacity="0.8"/>
  
  <!-- Neural network nodes -->
  <circle cx="20" cy="20" r="2" fill="#3b82f6"/>
  <circle cx="28" cy="20" r="2" fill="#3b82f6"/>
  <circle cx="24" cy="24" r="2" fill="#8b5cf6"/>
  <circle cx="20" cy="28" r="2" fill="#3b82f6"/>
  <circle cx="28" cy="28" r="2" fill="#3b82f6"/>
  
  <!-- Connecting lines -->
  <line x1="20" y1="20" x2="24" y2="24" stroke="#6366f1" stroke-width="1.5" opacity="0.6"/>
  <line x1="28" y1="20" x2="24" y2="24" stroke="#6366f1" stroke-width="1.5" opacity="0.6"/>
  <line x1="24" y1="24" x2="20" y2="28" stroke="#6366f1" stroke-width="1.5" opacity="0.6"/>
  <line x1="24" y1="24" x2="28" y2="28" stroke="#6366f1" stroke-width="1.5" opacity="0.6"/>
  
  <!-- Sparkle effects -->
  <path d="M12 12 L14 14 L12 16 L10 14 Z" fill="#fbbf24" opacity="0.8"/>
  <path d="M36 12 L37 13 L36 14 L35 13 Z" fill="#fbbf24" opacity="0.6"/>
  <path d="M36 36 L38 38 L36 40 L34 38 Z" fill="#fbbf24" opacity="0.8"/>
</svg>
