<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6"/>
      <stop offset="100%" style="stop-color:#a855f7"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#uiGradient)" opacity="0.1"/>
  
  <!-- Design canvas -->
  <rect x="10" y="12" width="28" height="24" fill="white" stroke="url(#uiGradient)" stroke-width="2" rx="4"/>
  
  <!-- UI elements -->
  <!-- Header -->
  <rect x="12" y="14" width="24" height="4" fill="url(#uiGradient)" opacity="0.3" rx="2"/>
  
  <!-- Buttons -->
  <rect x="12" y="20" width="6" height="3" fill="#8b5cf6" rx="1.5"/>
  <rect x="20" y="20" width="6" height="3" fill="white" stroke="#8b5cf6" rx="1.5"/>
  <rect x="28" y="20" width="6" height="3" fill="#ef4444" rx="1.5"/>
  
  <!-- Form elements -->
  <rect x="12" y="26" width="12" height="2" fill="#f3f4f6" stroke="#d1d5db" rx="1"/>
  <rect x="26" y="26" width="8" height="2" fill="#f3f4f6" stroke="#d1d5db" rx="1"/>
  
  <!-- Cards -->
  <rect x="12" y="30" width="10" height="4" fill="white" stroke="#e5e7eb" rx="2"/>
  <rect x="24" y="30" width="10" height="4" fill="white" stroke="#e5e7eb" rx="2"/>
  
  <!-- Design tools -->
  <circle cx="40" cy="16" r="3" fill="#8b5cf6" opacity="0.6"/>
  <rect x="38" y="22" width="4" height="4" fill="#a855f7" opacity="0.6" rx="1"/>
  <polygon points="38,30 42,30 40,34" fill="#8b5cf6" opacity="0.6"/>
  
  <!-- Magic wand effect -->
  <path d="M6 8 L8 6" stroke="#fbbf24" stroke-width="2" opacity="0.8"/>
  <path d="M6 6 L8 8" stroke="#fbbf24" stroke-width="2" opacity="0.8"/>
  <circle cx="7" cy="7" r="1" fill="#fbbf24" opacity="0.6"/>
</svg>
