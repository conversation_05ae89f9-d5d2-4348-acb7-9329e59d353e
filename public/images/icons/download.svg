<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="downloadGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#1d4ed8"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#downloadGradient)" opacity="0.1"/>
  
  <!-- Download arrow -->
  <path d="M24 8 L24 28" stroke="url(#downloadGradient)" stroke-width="3" stroke-linecap="round"/>
  <path d="M16 22 L24 30 L32 22" stroke="url(#downloadGradient)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Download tray -->
  <rect x="12" y="32" width="24" height="4" fill="url(#downloadGradient)" rx="2"/>
  
  <!-- Progress indicator -->
  <rect x="14" y="38" width="20" height="2" fill="#e5e7eb" rx="1"/>
  <rect x="14" y="38" width="12" height="2" fill="url(#downloadGradient)" rx="1">
    <animate attributeName="width" values="0;20;0" dur="3s" repeatCount="indefinite"/>
  </rect>
</svg>
