<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="deployGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#deployGradient)" opacity="0.1"/>
  
  <!-- Rocket body -->
  <ellipse cx="24" cy="28" rx="4" ry="12" fill="url(#deployGradient)"/>
  
  <!-- Rocket tip -->
  <path d="M20 16 L24 8 L28 16 Z" fill="url(#deployGradient)"/>
  
  <!-- Rocket fins -->
  <path d="M20 32 L16 38 L20 36 Z" fill="#059669"/>
  <path d="M28 32 L32 38 L28 36 Z" fill="#059669"/>
  
  <!-- Rocket window -->
  <circle cx="24" cy="22" r="2" fill="#60a5fa"/>
  
  <!-- Exhaust flames -->
  <path d="M22 40 L24 44 L26 40" fill="#f59e0b" opacity="0.8">
    <animateTransform attributeName="transform" type="scale" 
                      values="1;1.2;1" dur="0.5s" repeatCount="indefinite"/>
  </path>
  <path d="M21 42 L24 46 L27 42" fill="#ef4444" opacity="0.6">
    <animateTransform attributeName="transform" type="scale" 
                      values="1;1.3;1" dur="0.7s" repeatCount="indefinite"/>
  </path>
  
  <!-- Stars -->
  <circle cx="12" cy="12" r="1" fill="#fbbf24">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="36" cy="16" r="1" fill="#fbbf24">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="38" cy="32" r="1" fill="#fbbf24">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.8s" repeatCount="indefinite"/>
  </circle>
</svg>
