import type {
  Request as CloudflareRequest,
  ExecutionContext,
  ExportedHandler,
} from "@cloudflare/workers-types";
import { type ServerBuild, createRequestHandler } from "@remix-run/cloudflare";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore This file won't exist if it hasn't yet been built
import * as build from "./build/server";
import { getLoadContext } from "./load-context";

const handleRemixRequest = createRequestHandler(build as any);

export default {
  async fetch(request: any, env: Env, ctx: any) {
    try {
      const loadContext = getLoadContext({
        request: request as any,
        context: {
          cloudflare: {
            // This object matches the return value from Wrangler's
            // `getPlatformProxy` used during development via Remix's
            // `cloudflareDevProxyVitePlugin`:
            // https://developers.cloudflare.com/workers/wrangler/api/#getplatformproxy
            cf: request.cf,
            ctx: {
              waitUntil: ctx.waitUntil.bind(ctx),
              passThroughOnException: ctx.passThroughOnException.bind(ctx),
              props: {},
            },
            caches,
            env,
          },
        },
      });
      return await handleRemixRequest(request as any, loadContext);
    } catch (error) {
      console.log(error);
      return new Response("An unexpected error occurred", { status: 500 });
    }
  },
} as any;
