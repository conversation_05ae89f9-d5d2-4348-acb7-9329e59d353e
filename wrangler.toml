#:schema node_modules/wrangler/config-schema.json
name = "remix-cloudflare-neon-starter"

main = "./server.ts"
workers_dev = true
# https://developers.cloudflare.com/workers/platform/compatibility-dates
compatibility_date = "2024-09-26"

[assets]
# https://developers.cloudflare.com/workers/static-assets/binding/
directory = "./build/client"

[build]
command = "yarn build"

# R2 Storage Binding
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-cf-neon-starter-r2"
preview_bucket_name = "remix-cf-neon-starter-r2-preview"

# Cloudflare AI Binding
[ai]
binding = "AI"
