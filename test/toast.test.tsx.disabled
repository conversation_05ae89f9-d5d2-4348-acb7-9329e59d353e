import { toast } from "sonner";
// test/toast.test.tsx
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useNotify } from "~/lib/toast";

// Mock the sonner library
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

describe("useNotify Hook", () => {
  let notify: ReturnType<typeof useNotify>;

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    // Get a fresh instance of the hook's returned object
    notify = useNotify();
  });

  it("should call toast.success with title and description", () => {
    notify.success("Success Title", "Success message here");
    expect(toast.success).toHaveBeenCalledWith("Success Title", {
      description: "Success message here",
    });
  });

  it("should call toast.success with only title", () => {
    notify.success("Just Title");
    expect(toast.success).toHaveBeenCalledWith("Just Title");
  });

  it("should call toast.error with title and description", () => {
    notify.error("Error Title", "Error message here");
    expect(toast.error).toHaveBeenCalledWith("Error Title", { description: "Error message here" });
  });

  it("should call toast.error with only title", () => {
    notify.error("Just Error Title");
    expect(toast.error).toHaveBeenCalledWith("Just Error Title");
  });

  it("should call toast.warning with title and description", () => {
    notify.warning("Warning Title", "Warning message here");
    expect(toast.warning).toHaveBeenCalledWith("Warning Title", {
      description: "Warning message here",
    });
  });

  it("should call toast.warning with only title", () => {
    notify.warning("Just Warning Title");
    expect(toast.warning).toHaveBeenCalledWith("Just Warning Title");
  });

  it("should call toast.info with title and description", () => {
    notify.info("Info Title", "Info message here");
    expect(toast.info).toHaveBeenCalledWith("Info Title", { description: "Info message here" });
  });

  it("should call toast.info with only title", () => {
    notify.info("Just Info Title");
    expect(toast.info).toHaveBeenCalledWith("Just Info Title");
  });
});
