import { existsSync, readFileSync } from "fs";
import { join } from "path";
import { defineConfig } from "drizzle-kit";

// Environment variable loading utility
function loadEnvironmentVariables(): Record<string, string> {
  const envVars: Record<string, string> = {};

  // Try to load from .dev.vars (Cloudflare Workers development)
  const devVarsPath = join(process.cwd(), ".dev.vars");
  if (existsSync(devVarsPath)) {
    try {
      const devVars = readFileSync(devVarsPath, "utf-8");
      const vars = devVars.split("\n").reduce(
        (acc, line) => {
          const trimmedLine = line.trim();
          if (trimmedLine && !trimmedLine.startsWith("#")) {
            const [key, ...valueParts] = trimmedLine.split("=");
            if (key && valueParts.length > 0) {
              let value = valueParts.join("=").trim();
              // Remove quotes if present
              if (
                (value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))
              ) {
                value = value.slice(1, -1);
              }
              acc[key.trim()] = value;
            }
          }
          return acc;
        },
        {} as Record<string, string>
      );

      Object.assign(envVars, vars);
      console.log("✓ Loaded environment variables from .dev.vars");
    } catch (error) {
      console.warn("⚠️  Could not read .dev.vars file:", error);
    }
  }

  // Try to load from .env (fallback)
  const envPath = join(process.cwd(), ".env");
  if (existsSync(envPath)) {
    try {
      const envContent = readFileSync(envPath, "utf-8");
      const vars = envContent.split("\n").reduce(
        (acc, line) => {
          const trimmedLine = line.trim();
          if (trimmedLine && !trimmedLine.startsWith("#")) {
            const [key, ...valueParts] = trimmedLine.split("=");
            if (key && valueParts.length > 0) {
              let value = valueParts.join("=").trim();
              if (
                (value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))
              ) {
                value = value.slice(1, -1);
              }
              // Only set if not already set from .dev.vars
              if (!envVars[key.trim()]) {
                acc[key.trim()] = value;
              }
            }
          }
          return acc;
        },
        {} as Record<string, string>
      );

      Object.assign(envVars, vars);
      console.log("✓ Loaded additional environment variables from .env");
    } catch (error) {
      console.warn("⚠️  Could not read .env file:", error);
    }
  }

  return envVars;
}

// Load environment variables
const envVars = loadEnvironmentVariables();
Object.assign(process.env, envVars);

// Get database URL with validation
function getDatabaseUrl(): string {
  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    throw new Error(
      "DATABASE_URL environment variable is required but not found. " +
        "Please set it in your .dev.vars or .env file."
    );
  }

  // Validate URL format
  try {
    new URL(databaseUrl);
  } catch (error) {
    throw new Error(
      `Invalid DATABASE_URL format: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }

  console.log("✓ Database URL validated successfully");
  return databaseUrl;
}

export default defineConfig({
  // Schema configuration
  schema: "./app/lib/db/schema.ts",

  // Output directory for migrations
  out: "./drizzle",

  // Database dialect
  dialect: "postgresql",

  // Database credentials
  dbCredentials: {
    url: getDatabaseUrl(),
  },

  // Migration configuration
  migrations: {
    prefix: "timestamp", // Use timestamp prefix for migration files
    table: "__drizzle_migrations", // Custom migrations table name
    schema: "public", // Schema for migrations table
  },

  // Introspection configuration
  introspect: {
    casing: "preserve", // Preserve original casing from database
  },

  // Additional configuration for better development experience
  verbose: process.env.NODE_ENV === "development",
  strict: true, // Enable strict mode for better type safety

  // Breakpoints configuration (useful for debugging)
  breakpoints: process.env.NODE_ENV === "development",
});
