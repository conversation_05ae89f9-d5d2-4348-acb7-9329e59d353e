import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { AppState } from "./types";

const initialState = {
  isInitialized: false,
  isOnline: true,
};

export const useAppStore = create<AppState>()(
  devtools(
    (set) => ({
      ...initialState,

      setInitialized: (initialized) => {
        set({ isInitialized: initialized }, false, "setInitialized");
      },

      setOnline: (online) => {
        set({ isOnline: online }, false, "setOnline");
      },

      reset: () => {
        set(initialState, false, "reset");
      },
    }),
    {
      name: "app-store",
    }
  )
);

// Selector functions
export const selectIsInitialized = (state: AppState) => state.isInitialized;
export const selectIsOnline = (state: AppState) => state.isOnline;

// Convenience hooks
export const useIsInitialized = () => useAppStore(selectIsInitialized);
export const useIsOnline = () => useAppStore(selectIsOnline);

// Actions hooks
export const useAppActions = () => {
  const setInitialized = useAppStore((state) => state.setInitialized);
  const setOnline = useAppStore((state) => state.setOnline);
  const reset = useAppStore((state) => state.reset);

  return {
    setInitialized,
    setOnline,
    reset,
  };
};
