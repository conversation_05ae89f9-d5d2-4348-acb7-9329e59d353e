/**
 * SEO utilities for meta tags, Open Graph, Twitter Cards, and structured data
 */

export interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: "website" | "article" | "product";
  siteName?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  noFollow?: boolean;
  canonical?: string;
}

export interface MetaTag {
  name?: string;
  property?: string;
  content: string;
  key?: string;
}

/**
 * Generate comprehensive meta tags for SEO
 */
export function generateSEOTags(config: SEOConfig): MetaTag[] {
  const tags: MetaTag[] = [];

  // Basic meta tags
  if (config.description) {
    tags.push({ name: "description", content: config.description });
  }

  if (config.keywords) {
    tags.push({ name: "keywords", content: config.keywords });
  }

  if (config.author) {
    tags.push({ name: "author", content: config.author });
  }

  // Robots meta tag
  const robotsContent = [];
  if (config.noIndex) robotsContent.push("noindex");
  if (config.noFollow) robotsContent.push("nofollow");
  if (robotsContent.length === 0) {
    robotsContent.push("index", "follow");
  }
  tags.push({ name: "robots", content: robotsContent.join(", ") });

  // Canonical URL
  if (config.canonical) {
    tags.push({ name: "canonical", content: config.canonical });
  }

  // Open Graph tags
  if (config.title) {
    tags.push({ property: "og:title", content: config.title });
  }

  if (config.description) {
    tags.push({ property: "og:description", content: config.description });
  }

  if (config.type) {
    tags.push({ property: "og:type", content: config.type });
  }

  if (config.url) {
    tags.push({ property: "og:url", content: config.url });
  }

  if (config.image) {
    tags.push({ property: "og:image", content: config.image });
    tags.push({ property: "og:image:alt", content: config.title || "Image" });
  }

  if (config.siteName) {
    tags.push({ property: "og:site_name", content: config.siteName });
  }

  // Article-specific Open Graph tags
  if (config.type === "article") {
    if (config.author) {
      tags.push({ property: "article:author", content: config.author });
    }
    if (config.publishedTime) {
      tags.push({ property: "article:published_time", content: config.publishedTime });
    }
    if (config.modifiedTime) {
      tags.push({ property: "article:modified_time", content: config.modifiedTime });
    }
    if (config.section) {
      tags.push({ property: "article:section", content: config.section });
    }
    if (config.tags) {
      config.tags.forEach((tag) => {
        tags.push({ property: "article:tag", content: tag });
      });
    }
  }

  // Twitter Card tags
  tags.push({ name: "twitter:card", content: "summary_large_image" });

  if (config.title) {
    tags.push({ name: "twitter:title", content: config.title });
  }

  if (config.description) {
    tags.push({ name: "twitter:description", content: config.description });
  }

  if (config.image) {
    tags.push({ name: "twitter:image", content: config.image });
  }

  return tags;
}

/**
 * Convert SEO tags to Remix meta format
 */
export function seoTagsToRemixMeta(tags: MetaTag[]) {
  return tags.map((tag) => {
    const meta: any = { content: tag.content };

    if (tag.name) {
      meta.name = tag.name;
    }

    if (tag.property) {
      meta.property = tag.property;
    }

    if (tag.key) {
      meta.key = tag.key;
    }

    return meta;
  });
}

/**
 * Generate title with template support
 */
export function generateTitle(title: string, template?: string, siteName?: string): string {
  if (template) {
    return template.replace("%s", title);
  }

  if (siteName && title !== siteName) {
    return `${title} | ${siteName}`;
  }

  return title;
}

/**
 * Generate structured data for rich snippets
 */
export function generateStructuredData(config: {
  type: "WebSite" | "Organization" | "Article" | "Product";
  name: string;
  description?: string;
  url?: string;
  logo?: string;
  image?: string;
  author?: string;
  datePublished?: string;
  dateModified?: string;
  price?: string;
  currency?: string;
  availability?: string;
}) {
  const baseData = {
    "@context": "https://schema.org",
    "@type": config.type,
    name: config.name,
  };

  if (config.description) {
    (baseData as any).description = config.description;
  }

  if (config.url) {
    (baseData as any).url = config.url;
  }

  if (config.image) {
    (baseData as any).image = config.image;
  }

  // Type-specific properties
  switch (config.type) {
    case "WebSite":
      if (config.url) {
        (baseData as any).potentialAction = {
          "@type": "SearchAction",
          target: `${config.url}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string",
        };
      }
      break;

    case "Organization":
      if (config.logo) {
        (baseData as any).logo = config.logo;
      }
      break;

    case "Article":
      if (config.author) {
        (baseData as any).author = {
          "@type": "Person",
          name: config.author,
        };
      }
      if (config.datePublished) {
        (baseData as any).datePublished = config.datePublished;
      }
      if (config.dateModified) {
        (baseData as any).dateModified = config.dateModified;
      }
      break;

    case "Product":
      if (config.price && config.currency) {
        (baseData as any).offers = {
          "@type": "Offer",
          price: config.price,
          priceCurrency: config.currency,
          availability: config.availability || "https://schema.org/InStock",
        };
      }
      break;
  }

  return baseData;
}

/**
 * Generate hreflang links for internationalization
 */
// Removed generateHreflangLinks function

/**
 * Default SEO configuration
 */
export const defaultSEOConfig = {
  siteName: "Remix SaaS Starter",
  type: "website" as const,
  image: "/logo-light.png", // Default OG image
  author: "Remix SaaS Team",
};
