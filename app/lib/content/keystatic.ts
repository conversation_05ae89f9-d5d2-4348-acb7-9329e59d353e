import { createReader } from "@keystatic/core/reader";
import config from "../../../keystatic.config";

// Create a reader instance for accessing content
// Use safe process.cwd() check for client-side compatibility
export const reader = createReader(typeof process !== "undefined" ? process.cwd() : "/", config);

// Type definitions for content
export interface BlogPost {
  slug: string;
  title: string;
  publishedDate: string;
  author: string;
  excerpt: string;
  featuredImage?: string;
  tags: string[];
  category: string;
  status: "draft" | "published" | "archived";
  seo: {
    title?: string;
    description?: string;
    keywords: string[];
  };
  content: any; // Keystatic document content
}

export interface Page {
  slug: string;
  title: string;
  description: string;
  status: "draft" | "published";
  seo: {
    title?: string;
    description?: string;
  };
  content: any;
}

export interface Testimonial {
  slug: string;
  name: string;
  company: string;
  position: string;
  avatar?: string;
  rating: string;
  testimonial: string;
  featured: boolean;
}

export interface FAQ {
  slug: string;
  question: string;
  answer: string;
  category: string;
  order: number;
}

export interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  logo?: string;
  favicon?: string;
  socialMedia: {
    twitter?: string;
    github?: string;
    linkedin?: string;
    discord?: string;
  };
  analytics: {
    googleAnalyticsId?: string;
    googleTagManagerId?: string;
  };
  seo: {
    defaultTitle: string;
    defaultDescription: string;
    defaultImage?: string;
  };
}

export interface HomepageContent {
  hero: {
    title: string;
    subtitle: string;
    ctaText: string;
    ctaUrl: string;
    backgroundImage?: string;
  };
  features: Array<{
    title: string;
    description: string;
    icon: string;
  }>;
  stats: Array<{
    label: string;
    value: string;
    description: string;
  }>;
}

// Content fetching functions
export async function getAllPosts(): Promise<BlogPost[]> {
  try {
    const posts = await reader.collections.posts.all();
    return posts
      .map((post) => ({
        slug: post.slug,
        ...post.entry,
      }))
      .filter((post) => post.status === "published")
      .sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime());
  } catch (error) {
    console.error("Error fetching posts:", error);
    return [];
  }
}

export async function getPost(slug: string): Promise<BlogPost | null> {
  try {
    const post = await reader.collections.posts.read(slug);
    if (!post) return null;

    return {
      slug,
      ...post,
    };
  } catch (error) {
    console.error(`Error fetching post ${slug}:`, error);
    return null;
  }
}

export async function getPostsByCategory(category: string): Promise<BlogPost[]> {
  try {
    const allPosts = await getAllPosts();
    return allPosts.filter((post) => post.category === category);
  } catch (error) {
    console.error(`Error fetching posts by category ${category}:`, error);
    return [];
  }
}

export async function getPostsByTag(tag: string): Promise<BlogPost[]> {
  try {
    const allPosts = await getAllPosts();
    return allPosts.filter((post) => post.tags.includes(tag));
  } catch (error) {
    console.error(`Error fetching posts by tag ${tag}:`, error);
    return [];
  }
}

export async function getAllPages(): Promise<Page[]> {
  try {
    const pages = await reader.collections.pages.all();
    return pages
      .map((page) => ({
        slug: page.slug,
        ...page.entry,
      }))
      .filter((page) => page.status === "published");
  } catch (error) {
    console.error("Error fetching pages:", error);
    return [];
  }
}

export async function getPage(slug: string): Promise<Page | null> {
  try {
    const page = await reader.collections.pages.read(slug);
    if (!page) return null;

    return {
      slug,
      ...page,
    };
  } catch (error) {
    console.error(`Error fetching page ${slug}:`, error);
    return null;
  }
}

export async function getAllTestimonials(): Promise<Testimonial[]> {
  try {
    const testimonials = await reader.collections.testimonials.all();
    return testimonials.map((testimonial) => ({
      slug: testimonial.slug,
      ...testimonial.entry,
    }));
  } catch (error) {
    console.error("Error fetching testimonials:", error);
    return [];
  }
}

export async function getFeaturedTestimonials(): Promise<Testimonial[]> {
  try {
    const allTestimonials = await getAllTestimonials();
    return allTestimonials.filter((testimonial) => testimonial.featured);
  } catch (error) {
    console.error("Error fetching featured testimonials:", error);
    return [];
  }
}

export async function getAllFAQs(): Promise<FAQ[]> {
  try {
    const faqs = await reader.collections.faqs.all();
    return faqs
      .map((faq) => ({
        slug: faq.slug,
        ...faq.entry,
      }))
      .sort((a, b) => a.order - b.order);
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    return [];
  }
}

export async function getFAQsByCategory(category: string): Promise<FAQ[]> {
  try {
    const allFAQs = await getAllFAQs();
    return allFAQs.filter((faq) => faq.category === category);
  } catch (error) {
    console.error(`Error fetching FAQs by category ${category}:`, error);
    return [];
  }
}

export async function getSiteSettings(): Promise<SiteSettings | null> {
  try {
    const settings = await reader.singletons.settings.read();
    return settings;
  } catch (error) {
    console.error("Error fetching site settings:", error);
    return null;
  }
}

export async function getHomepageContent(): Promise<HomepageContent | null> {
  try {
    const homepage = await reader.singletons.homepage.read();
    return homepage;
  } catch (error) {
    console.error("Error fetching homepage content:", error);
    return null;
  }
}

// Utility functions
export function formatDate(date: Date | string): string {
  if (typeof date === "string") {
    date = new Date(date);
  }
  return date.toLocaleDateString({
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

export function getReadingTime(content: any): number {
  // Estimate reading time based on content
  // This is a simplified calculation
  const wordsPerMinute = 200;
  const textContent = JSON.stringify(content);
  const wordCount = textContent.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

export function generateExcerpt(content: any, maxLength = 160): string {
  // Extract plain text from Keystatic document content
  const textContent = JSON.stringify(content);
  const plainText = textContent
    .replace(/[{}[\]"]/g, " ")
    .replace(/\s+/g, " ")
    .trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  return plainText.substring(0, maxLength).trim() + "...";
}
