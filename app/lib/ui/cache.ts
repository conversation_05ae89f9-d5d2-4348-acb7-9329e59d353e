// app/lib/cache.ts
// 本地存储缓存工具函数，参考 shipany 项目实现

// 获取当前时间戳
const getTimestamp = (): number => {
  return Math.floor(Date.now() / 1000);
};

// 从缓存中获取数据
export const cacheGet = (key: string): string | null => {
  if (typeof window === "undefined") {
    return null; // SSR 环境下返回 null
  }

  const valueWithExpires = localStorage.getItem(key);
  if (!valueWithExpires) {
    return null;
  }

  const valueArr = valueWithExpires.split(":");
  if (!valueArr || valueArr.length < 2) {
    return null;
  }

  const expiresAt = Number(valueArr[0]);
  const currTimestamp = getTimestamp();

  if (expiresAt !== -1 && expiresAt < currTimestamp) {
    // 值已过期
    cacheRemove(key);
    return null;
  }

  const searchStr = valueArr[0] + ":";
  const value = valueWithExpires.replace(searchStr, "");

  return value;
};

// 设置数据到缓存
// expiresAt: 绝对时间戳，-1 表示永不过期
export const cacheSet = (key: string, value: string, expiresAt: number) => {
  if (typeof window === "undefined") {
    return; // SSR 环境下不执行
  }

  const valueWithExpires = expiresAt + ":" + value;
  localStorage.setItem(key, valueWithExpires);
};

// 从缓存中移除数据
export const cacheRemove = (key: string) => {
  if (typeof window === "undefined") {
    return; // SSR 环境下不执行
  }

  localStorage.removeItem(key);
};

// 清空所有缓存数据
export const cacheClear = () => {
  if (typeof window === "undefined") {
    return; // SSR 环境下不执行
  }

  localStorage.clear();
};
