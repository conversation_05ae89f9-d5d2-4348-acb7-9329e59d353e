// app/lib/toast.ts
import { toast } from "sonner";

/**
 * Custom hook to display toast notifications using <PERSON><PERSON>.
 * Provides methods for different notification types (success, error, warning, info).
 *
 * @returns An object with methods to trigger notifications.
 * @example
 * const { success, error } = useNotify();
 * success("Profile Updated", "Your changes have been saved.");
 * error("Upload Failed", "The file could not be uploaded.");
 */
export function useNotify() {
  return {
    /**
     * Displays a success toast.
     * @param title The main title of the toast.
     * @param message Optional description displayed below the title.
     */
    success: (title: string, message?: string) => {
      if (message) {
        toast.success(title, { description: message });
      } else {
        toast.success(title);
      }
    },
    /**
     * Displays an error toast.
     * @param title The main title of the toast.
     * @param message Optional description displayed below the title.
     */
    error: (title: string, message?: string) => {
      if (message) {
        toast.error(title, { description: message });
      } else {
        toast.error(title);
      }
    },
    /**
     * Displays a warning toast.
     * @param title The main title of the toast.
     * @param message Optional description displayed below the title.
     */
    warning: (title: string, message?: string) => {
      if (message) {
        toast.warning(title, { description: message });
      } else {
        toast.warning(title);
      }
    },
    /**
     * Displays an informational toast.
     * @param title The main title of the toast.
     * @param message Optional description displayed below the title.
     */
    info: (title: string, message?: string) => {
      if (message) {
        toast.info(title, { description: message });
      } else {
        toast.info(title);
      }
    },
  };
}
