/**
 * Authentication Configuration
 * Manages feature flags and configuration for different auth providers
 */

export type AuthProvider = "google-one-tap" | "neon-auth" | "both";

export interface AuthConfig {
  provider: AuthProvider;
  primaryProvider: "google-one-tap" | "neon-auth"; // 主要认证方式
  googleOneTap: {
    enabled: boolean;
    clientId?: string;
  };
  neonAuth: {
    enabled: boolean;
    projectId?: string;
    publishableKey?: string;
    secretKey?: string;
  };
}

// Get authentication configuration from environment
export const getAuthConfig = (env?: Record<string, string | undefined>): AuthConfig => {
  const neonAuthEnabled = env?.NEON_AUTH_ENABLED === "true";
  const googleOneTapEnabled = env?.ONE_TAP_ENABLED === "true";

  // 支持同时启用两个认证系统
  const bothEnabled = neonAuthEnabled && googleOneTapEnabled;

  // 确定主要认证方式（当两个都启用时）
  const primaryProvider =
    env?.PRIMARY_AUTH_PROVIDER === "google-one-tap" ? "google-one-tap" : "neon-auth";

  let provider: AuthProvider;
  if (bothEnabled) {
    provider = "both";
  } else if (neonAuthEnabled) {
    provider = "neon-auth";
  } else {
    provider = "google-one-tap";
  }

  return {
    provider,
    primaryProvider,
    googleOneTap: {
      enabled: googleOneTapEnabled,
      clientId: env?.GOOGLE_CLIENT_ID,
    },
    neonAuth: {
      enabled: neonAuthEnabled,
      projectId: env?.VITE_STACK_PROJECT_ID,
      publishableKey: env?.VITE_STACK_PUBLISHABLE_CLIENT_KEY,
      secretKey: env?.STACK_SECRET_SERVER_KEY,
    },
  };
};

// Validate auth configuration
export const validateAuthConfig = (config: AuthConfig): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (config.provider === "google-one-tap") {
    if (config.googleOneTap.enabled && !config.googleOneTap.clientId) {
      errors.push("Google One Tap is enabled but GOOGLE_CLIENT_ID is not configured");
    }
  }

  if (config.provider === "neon-auth") {
    if (config.neonAuth.enabled) {
      if (!config.neonAuth.projectId) {
        errors.push("Neon Auth is enabled but VITE_STACK_PROJECT_ID is not configured");
      }
      if (!config.neonAuth.publishableKey) {
        errors.push("Neon Auth is enabled but VITE_STACK_PUBLISHABLE_CLIENT_KEY is not configured");
      }
      if (!config.neonAuth.secretKey) {
        errors.push("Neon Auth is enabled but STACK_SECRET_SERVER_KEY is not configured");
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

// Client-side auth configuration (safe for browser)
export const getClientAuthConfig = (env?: Record<string, string | undefined>) => {
  const config = getAuthConfig(env);

  return {
    provider: config.provider,
    googleOneTap: {
      enabled: config.googleOneTap.enabled,
      clientId: config.googleOneTap.clientId,
    },
    neonAuth: {
      enabled: config.neonAuth.enabled,
      projectId: config.neonAuth.projectId,
      publishableKey: config.neonAuth.publishableKey,
      // Don't expose secret key to client
    },
  };
};
