/**
 * Neon Auth Client Configuration for Remix + Cloudflare Workers
 * Experimental implementation adapting Neon Auth for Remix
 */

import { StackClientApp } from "@stackframe/react";

// Custom navigation adapter for Remix
const createRemixNavigationAdapter = () => {
  return {
    push: (url: string) => {
      // In Remix, we use window.location for navigation
      // This is a simplified approach for the experimental implementation
      if (typeof window !== "undefined") {
        window.location.href = url;
      }
    },
    replace: (url: string) => {
      if (typeof window !== "undefined") {
        window.location.replace(url);
      }
    },
  };
};

// Neon Auth client configuration
export const createNeonAuthClient = (config: {
  projectId?: string;
  publishableClientKey?: string;
}) => {
  if (!config.projectId || !config.publishableClientKey) {
    console.warn("Neon Auth: Missing required configuration. Neon Auth will not be initialized.");
    return null;
  }

  try {
    const stackClientApp = new StackClientApp({
      projectId: config.projectId,
      publishableClientKey: config.publishableClientKey,
      tokenStore: "cookie",
      // Use a simple redirect method for experimental implementation
      redirectMethod: (url: string) => {
        if (typeof window !== "undefined") {
          window.location.href = url;
        }
      },
    });

    return stackClientApp;
  } catch (error) {
    console.error("Neon Auth: Failed to initialize client:", error);
    return null;
  }
};

// Environment variable helpers for client-side
export const getNeonAuthConfig = () => {
  // In Remix with Cloudflare Workers, we need to be careful about environment variables
  // Client-side environment variables should be prefixed with VITE_
  if (typeof window === "undefined") {
    return {
      projectId: undefined,
      publishableClientKey: undefined,
    };
  }

  return {
    projectId: (window as any).__NEON_AUTH_PROJECT_ID__ || import.meta.env?.VITE_STACK_PROJECT_ID,
    publishableClientKey:
      (window as any).__NEON_AUTH_PUBLISHABLE_KEY__ ||
      import.meta.env?.VITE_STACK_PUBLISHABLE_CLIENT_KEY,
  };
};

// Check if Neon Auth is enabled
export const isNeonAuthEnabled = () => {
  if (typeof window === "undefined") {
    return false;
  }

  return (window as any).__NEON_AUTH_ENABLED__ === "true";
};

// Initialize Neon Auth client with environment configuration
export const neonAuthClient = (() => {
  if (typeof window === "undefined") {
    return null;
  }

  const config = getNeonAuthConfig();
  return createNeonAuthClient(config);
})();
