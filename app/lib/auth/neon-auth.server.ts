/**
 * Neon Auth Server-side Utilities for Remix + Cloudflare Workers
 * Experimental implementation for backend integration
 */

import type { AuthenticatedUser } from "./middleware.server";

// Neon Auth server configuration
export interface NeonAuthServerConfig {
  projectId?: string;
  secretKey?: string;
  enabled?: boolean;
}

// Get Neon Auth configuration from environment
export const getNeonAuthServerConfig = (
  env?: Record<string, string | undefined>
): NeonAuthServerConfig => {
  return {
    projectId: env?.VITE_STACK_PROJECT_ID,
    secretKey: env?.STACK_SECRET_SERVER_KEY,
    enabled: env?.NEON_AUTH_ENABLED === "true",
  };
};

// Validate Neon Auth token (placeholder implementation)
export const validateNeonAuthToken = async (
  token: string,
  config: NeonAuthServerConfig
): Promise<{ valid: boolean; user?: any; error?: string }> => {
  try {
    if (!config.secretKey) {
      return { valid: false, error: "Neon Auth secret key not configured" };
    }

    // TODO: Implement actual Neon Auth token validation
    // This would typically involve calling Neon Auth's validation endpoint
    // For now, this is a placeholder that always returns invalid

    console.warn("Neon Auth: Token validation not yet implemented");
    return { valid: false, error: "Token validation not implemented" };
  } catch (error) {
    console.error("Neon Auth: Token validation error:", error);
    return { valid: false, error: "Token validation failed" };
  }
};

// Get user from Neon Auth token
export const getNeonAuthUser = async (
  request: Request,
  env?: Record<string, string | undefined>
): Promise<{ success: boolean; user?: AuthenticatedUser; error?: string }> => {
  try {
    const config = getNeonAuthServerConfig(env);

    if (!config.enabled) {
      return { success: false, error: "Neon Auth not enabled" };
    }

    // Extract token from Authorization header or cookies
    const authHeader = request.headers.get("Authorization");
    let token: string | null = null;

    if (authHeader?.startsWith("Bearer ")) {
      token = authHeader.substring(7);
    } else {
      // Try to get token from cookies
      const cookieHeader = request.headers.get("Cookie");
      if (cookieHeader) {
        const cookies = cookieHeader.split(";").reduce(
          (acc, cookie) => {
            const [key, value] = cookie.trim().split("=");
            acc[key] = value;
            return acc;
          },
          {} as Record<string, string>
        );

        // Neon Auth typically uses 'stack-auth-token' or similar
        token = cookies["stack-auth-token"] || cookies["neon-auth-token"];
      }
    }

    if (!token) {
      return { success: false, error: "No Neon Auth token found" };
    }

    const validation = await validateNeonAuthToken(token, config);

    if (!validation.valid) {
      return { success: false, error: validation.error || "Invalid token" };
    }

    // Convert Neon Auth user to our AuthenticatedUser format
    const user: AuthenticatedUser = {
      id: validation.user?.id || "",
      uuid: validation.user?.uuid || validation.user?.id || "",
      email: validation.user?.email || "",
      name: validation.user?.name || validation.user?.displayName || "",
      avatar: validation.user?.avatar || validation.user?.profilePictureUrl,
      credits: validation.user?.credits || 0,
      createdAt: new Date(validation.user?.createdAt || Date.now()),
      sessionId: validation.user?.sessionId || "neon-auth-session",
    };

    return { success: true, user };
  } catch (error) {
    console.error("Neon Auth: Get user error:", error);
    return { success: false, error: "Failed to get user" };
  }
};

// Check if request is using Neon Auth
export const isNeonAuthRequest = (
  request: Request,
  env?: Record<string, string | undefined>
): boolean => {
  const config = getNeonAuthServerConfig(env);

  if (!config.enabled) {
    return false;
  }

  // Check for Neon Auth specific headers or cookies
  const authHeader = request.headers.get("Authorization");
  const cookieHeader = request.headers.get("Cookie");

  // Look for Neon Auth specific tokens
  if (authHeader?.startsWith("Bearer ")) {
    return true; // Could be Neon Auth token
  }

  if (cookieHeader?.includes("stack-auth-token") || cookieHeader?.includes("neon-auth-token")) {
    return true;
  }

  return false;
};
