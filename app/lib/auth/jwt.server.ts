/**
 * JWT Token Management for Remix + Cloudflare Workers
 * Handles JWT creation, verification, and session management
 */

import { SignJWT, jwtVerify } from "jose";
import type { JWTPayload } from "jose";

// JWT Configuration
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || "your-super-secret-jwt-key-change-in-production"
);
const JWT_ALGORITHM = "HS256";
const JWT_EXPIRES_IN = "7d"; // 7 days
const REFRESH_TOKEN_EXPIRES_IN = "30d"; // 30 days

// JWT Payload interface
export interface AuthJWTPayload extends JWTPayload {
  userId: string;
  userUuid: string;
  email: string;
  name: string;
  avatar?: string;
  sessionId: string;
  type: "access" | "refresh";
}

/**
 * Create a JWT token
 */
export async function createJWT(
  payload: Omit<AuthJWTPayload, "iat" | "exp" | "jti">,
  expiresIn: string = JWT_EXPIRES_IN
): Promise<string> {
  const jwt = new SignJWT(payload)
    .setProtectedHeader({ alg: JWT_ALGORITHM })
    .setIssuedAt()
    .setExpirationTime(expiresIn)
    .setJti(crypto.randomUUID());

  return await jwt.sign(JWT_SECRET);
}

/**
 * Verify and decode a JWT token
 */
export async function verifyJWT(token: string): Promise<AuthJWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload as AuthJWTPayload;
  } catch (error) {
    console.error("JWT verification failed:", error);
    return null;
  }
}

/**
 * Create access and refresh tokens
 */
export async function createTokenPair(
  user: {
    id: string;
    uuid: string;
    email: string;
    name: string;
    avatar?: string;
  },
  sessionId: string
): Promise<{
  accessToken: string;
  refreshToken: string;
  accessExpiresAt: Date;
  refreshExpiresAt: Date;
}> {
  const basePayload = {
    userId: user.id,
    userUuid: user.uuid,
    email: user.email,
    name: user.name,
    avatar: user.avatar,
    sessionId,
  };

  const [accessToken, refreshToken] = await Promise.all([
    createJWT({ ...basePayload, type: "access" }, JWT_EXPIRES_IN),
    createJWT({ ...basePayload, type: "refresh" }, REFRESH_TOKEN_EXPIRES_IN),
  ]);

  const now = new Date();
  const accessExpiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days
  const refreshExpiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days

  return {
    accessToken,
    refreshToken,
    accessExpiresAt,
    refreshExpiresAt,
  };
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(request: Request): string | null {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }
  return authHeader.substring(7);
}

/**
 * Extract token from cookie
 */
export function extractTokenFromCookie(request: Request, cookieName = "auth-token"): string | null {
  const cookieHeader = request.headers.get("Cookie");
  if (!cookieHeader) return null;

  const cookies = cookieHeader.split(";").reduce(
    (acc, cookie) => {
      const [key, value] = cookie.trim().split("=");
      acc[key] = value;
      return acc;
    },
    {} as Record<string, string>
  );

  return cookies[cookieName] || null;
}

/**
 * Get user IP address from request
 */
export function getUserIP(request: Request): string {
  // Check Cloudflare headers first
  const cfConnectingIP = request.headers.get("CF-Connecting-IP");
  if (cfConnectingIP) return cfConnectingIP;

  // Check other common headers
  const xForwardedFor = request.headers.get("X-Forwarded-For");
  if (xForwardedFor) {
    return xForwardedFor.split(",")[0].trim();
  }

  const xRealIP = request.headers.get("X-Real-IP");
  if (xRealIP) return xRealIP;

  // Fallback
  return "unknown";
}

/**
 * Get user agent from request
 */
export function getUserAgent(request: Request): string {
  return request.headers.get("User-Agent") || "unknown";
}

/**
 * Create secure cookie options for Cloudflare Workers
 */
export function createCookieOptions(maxAge: number = 7 * 24 * 60 * 60): string {
  const options = [`Max-Age=${maxAge}`, "HttpOnly", "Secure", "SameSite=Lax", "Path=/"];

  return options.join("; ");
}

/**
 * Set authentication cookies
 */
export function setAuthCookies(
  accessToken: string,
  refreshToken: string,
  accessMaxAge: number = 7 * 24 * 60 * 60,
  refreshMaxAge: number = 30 * 24 * 60 * 60
): Headers {
  const headers = new Headers();

  headers.append("Set-Cookie", `auth-token=${accessToken}; ${createCookieOptions(accessMaxAge)}`);

  headers.append(
    "Set-Cookie",
    `refresh-token=${refreshToken}; ${createCookieOptions(refreshMaxAge)}`
  );

  return headers;
}

/**
 * Clear authentication cookies
 */
export function clearAuthCookies(): Headers {
  const headers = new Headers();

  headers.append("Set-Cookie", "auth-token=; Max-Age=0; HttpOnly; Secure; SameSite=Lax; Path=/");

  headers.append("Set-Cookie", "refresh-token=; Max-Age=0; HttpOnly; Secure; SameSite=Lax; Path=/");

  return headers;
}
