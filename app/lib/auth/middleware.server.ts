/**
 * Authentication Middleware for Remix + Cloudflare Workers
 * Handles user authentication and session management
 */

import { redirect } from "@remix-run/cloudflare";
import { and, eq } from "drizzle-orm";
import { createDbFromEnv } from "~/lib/db";
import { sessions, users } from "~/lib/db/schema";
import {
  type AuthJWTPayload,
  extractTokenFromCookie,
  extractTokenFromHeader,
  verifyJWT,
} from "./jwt.server";

// User interface for authenticated requests
export interface AuthenticatedUser {
  id: string;
  uuid: string;
  email: string;
  name: string;
  avatar?: string;
  credits: number;
  createdAt: Date;
  sessionId: string;
}

// Authentication result types
export type AuthResult =
  | { success: true; user: AuthenticatedUser }
  | { success: false; error: string; redirectTo?: string };

/**
 * Get current user from request
 * Checks both Authorization header and cookies
 */
export async function getUser(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<AuthResult> {
  try {
    // Try to extract token from header first, then cookie
    let token = extractTokenFromHeader(request);
    if (!token) {
      token = extractTokenFromCookie(request);
    }

    if (!token) {
      return {
        success: false,
        error: "No authentication token found",
        redirectTo: "/auth/signin",
      };
    }

    // Verify JWT token
    const payload = await verifyJWT(token);
    if (!payload || payload.type !== "access") {
      return {
        success: false,
        error: "Invalid or expired token",
        redirectTo: "/auth/signin",
      };
    }

    // Get database connection
    const db = createDbFromEnv(env);

    // Verify session is still active
    const session = await db
      .select()
      .from(sessions)
      .where(and(eq(sessions.id, payload.sessionId), eq(sessions.isActive, true)))
      .limit(1);

    if (session.length === 0) {
      return {
        success: false,
        error: "Session not found or inactive",
        redirectTo: "/auth/signin",
      };
    }

    // Check if session has expired
    const sessionData = session[0];
    if (sessionData.expiresAt < new Date()) {
      // Mark session as inactive
      await db.update(sessions).set({ isActive: false }).where(eq(sessions.id, payload.sessionId));

      return {
        success: false,
        error: "Session expired",
        redirectTo: "/auth/signin",
      };
    }

    // Get user data
    const user = await db.select().from(users).where(eq(users.id, payload.userId)).limit(1);

    if (user.length === 0) {
      return {
        success: false,
        error: "User not found",
        redirectTo: "/auth/signin",
      };
    }

    const userData = user[0];

    return {
      success: true,
      user: {
        id: userData.id,
        uuid: userData.uuid,
        email: userData.email,
        name: userData.name,
        avatar: userData.avatar || undefined,
        credits: userData.credits,
        createdAt: userData.createdAt,
        sessionId: payload.sessionId,
      },
    };
  } catch (error) {
    console.error("Authentication error:", error);
    return {
      success: false,
      error: "Authentication failed",
      redirectTo: "/auth/signin",
    };
  }
}

/**
 * Require authentication - throws redirect if not authenticated
 */
export async function requireAuth(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<AuthenticatedUser> {
  const result = await getUser(request, env);

  if (!result.success) {
    throw redirect(result.redirectTo || "/auth/signin");
  }

  return result.user;
}

/**
 * Optional authentication - returns user if authenticated, null otherwise
 */
export async function optionalAuth(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<AuthenticatedUser | null> {
  const result = await getUser(request, env);
  return result.success ? result.user : null;
}

/**
 * Check if user is authenticated (boolean result)
 */
export async function isAuthenticated(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<boolean> {
  const result = await getUser(request, env);
  return result.success;
}

/**
 * Logout user by deactivating session
 */
export async function logoutUser(
  sessionId: string,
  env?: Record<string, string | undefined>
): Promise<void> {
  try {
    const db = createDbFromEnv(env);

    await db
      .update(sessions)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(sessions.id, sessionId));
  } catch (error) {
    console.error("Logout error:", error);
    // Don't throw error for logout - just log it
  }
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions(
  env?: Record<string, string | undefined>
): Promise<void> {
  try {
    const db = createDbFromEnv(env);
    const now = new Date();

    await db
      .update(sessions)
      .set({ isActive: false })
      .where(
        and(
          eq(sessions.isActive, true)
          // Session has expired
          // Note: Using string comparison since Drizzle might not handle date comparison well
        )
      );
  } catch (error) {
    console.error("Session cleanup error:", error);
  }
}

/**
 * Get user by session token (for API routes)
 */
export async function getUserBySessionToken(
  sessionToken: string,
  env?: Record<string, string | undefined>
): Promise<AuthenticatedUser | null> {
  try {
    const db = createDbFromEnv(env);

    const sessionData = await db
      .select({
        session: sessions,
        user: users,
      })
      .from(sessions)
      .innerJoin(users, eq(sessions.userId, users.id))
      .where(and(eq(sessions.sessionToken, sessionToken), eq(sessions.isActive, true)))
      .limit(1);

    if (sessionData.length === 0) {
      return null;
    }

    const { session, user } = sessionData[0];

    // Check if session has expired
    if (session.expiresAt < new Date()) {
      // Mark session as inactive
      await db.update(sessions).set({ isActive: false }).where(eq(sessions.id, session.id));

      return null;
    }

    return {
      id: user.id,
      uuid: user.uuid,
      email: user.email,
      name: user.name,
      avatar: user.avatar || undefined,
      credits: user.credits,
      createdAt: user.createdAt,
      sessionId: session.id,
    };
  } catch (error) {
    console.error("Get user by session token error:", error);
    return null;
  }
}
