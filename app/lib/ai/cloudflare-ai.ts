/**
 * Cloudflare Workers AI Service
 * Provides text generation, embeddings, and image classification using Cloudflare AI
 */

export interface CloudflareAIModels {
  textGeneration: {
    "llama-3.2-3b": "@cf/meta/llama-3.2-3b-instruct";
    "llama-3-8b": "@cf/meta/llama-3-8b-instruct";
    "llama-guard-3-8b": "@cf/meta/llama-guard-3-8b";
    "llama-2-7b": "@cf/meta/llama-2-7b-chat-fp16";
  };
  textClassification: {
    "distilbert-sst-2": "@cf/huggingface/distilbert-sst-2-int8";
  };
  textEmbeddings: {
    "bge-m3": "@cf/baai/bge-m3";
  };
  imageClassification: {
    "resnet-50": "@cf/microsoft/resnet-50";
  };
  speechRecognition: {
    whisper: "@cf/openai/whisper";
  };
}

export const CLOUDFLARE_AI_MODELS: CloudflareAIModels = {
  textGeneration: {
    "llama-3.2-3b": "@cf/meta/llama-3.2-3b-instruct",
    "llama-3-8b": "@cf/meta/llama-3-8b-instruct",
    "llama-guard-3-8b": "@cf/meta/llama-guard-3-8b",
    "llama-2-7b": "@cf/meta/llama-2-7b-chat-fp16",
  },
  textClassification: {
    "distilbert-sst-2": "@cf/huggingface/distilbert-sst-2-int8",
  },
  textEmbeddings: {
    "bge-m3": "@cf/baai/bge-m3",
  },
  imageClassification: {
    "resnet-50": "@cf/microsoft/resnet-50",
  },
  speechRecognition: {
    whisper: "@cf/openai/whisper",
  },
};

export interface CloudflareAITextRequest {
  messages: Array<{
    role: "system" | "user" | "assistant";
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

export interface CloudflareAIEmbeddingRequest {
  text: string | string[];
}

export interface CloudflareAIImageClassificationRequest {
  image: ArrayBuffer | Uint8Array;
}

export interface CloudflareAISpeechRequest {
  audio: ArrayBuffer | Uint8Array;
}

export class CloudflareAIService {
  constructor(private ai: Ai) {}

  /**
   * Generate text using Cloudflare AI
   */
  async generateText(
    model: keyof CloudflareAIModels["textGeneration"],
    request: CloudflareAITextRequest
  ): Promise<any> {
    const modelId = CLOUDFLARE_AI_MODELS.textGeneration[model];

    try {
      const response = await this.ai.run(modelId, request);
      return response;
    } catch (error) {
      throw new Error(
        `Cloudflare AI text generation failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Generate embeddings using Cloudflare AI
   */
  async generateEmbeddings(
    model: keyof CloudflareAIModels["textEmbeddings"],
    request: CloudflareAIEmbeddingRequest
  ): Promise<any> {
    const modelId = CLOUDFLARE_AI_MODELS.textEmbeddings[model];

    try {
      const response = await this.ai.run(modelId, request);
      return response;
    } catch (error) {
      throw new Error(
        `Cloudflare AI embeddings failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Classify text using Cloudflare AI
   */
  async classifyText(
    model: keyof CloudflareAIModels["textClassification"],
    text: string
  ): Promise<any> {
    const modelId = CLOUDFLARE_AI_MODELS.textClassification[model];

    try {
      const response = await this.ai.run(modelId, { text });
      return response;
    } catch (error) {
      throw new Error(
        `Cloudflare AI text classification failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Classify images using Cloudflare AI
   */
  async classifyImage(
    model: keyof CloudflareAIModels["imageClassification"],
    request: CloudflareAIImageClassificationRequest
  ): Promise<any> {
    const modelId = CLOUDFLARE_AI_MODELS.imageClassification[model];

    try {
      const response = await this.ai.run(modelId, request);
      return response;
    } catch (error) {
      throw new Error(
        `Cloudflare AI image classification failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Convert speech to text using Cloudflare AI
   */
  async speechToText(
    model: keyof CloudflareAIModels["speechRecognition"],
    request: CloudflareAISpeechRequest
  ): Promise<any> {
    const modelId = CLOUDFLARE_AI_MODELS.speechRecognition[model];

    try {
      const response = await this.ai.run(modelId, request);
      return response;
    } catch (error) {
      throw new Error(
        `Cloudflare AI speech recognition failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Get available models for a specific task
   */
  getAvailableModels(task: keyof CloudflareAIModels): string[] {
    return Object.keys(CLOUDFLARE_AI_MODELS[task]);
  }

  /**
   * Get model ID for a specific model name
   */
  getModelId(task: keyof CloudflareAIModels, model: string): string | undefined {
    const models = CLOUDFLARE_AI_MODELS[task] as Record<string, string>;
    return models[model];
  }

  /**
   * Check if a model is available for a specific task
   */
  isModelAvailable(task: keyof CloudflareAIModels, model: string): boolean {
    const models = CLOUDFLARE_AI_MODELS[task] as Record<string, string>;
    return model in models;
  }
}

/**
 * Create a CloudflareAIService instance
 */
export function createCloudflareAIService(ai: Ai): CloudflareAIService {
  return new CloudflareAIService(ai);
}

/**
 * Utility function to validate Cloudflare AI availability
 */
export function validateCloudflareAI(env: any): boolean {
  return !!env?.AI;
}

/**
 * Get all available Cloudflare AI models
 */
export function getAllCloudflareAIModels(): Record<string, string[]> {
  return {
    textGeneration: Object.keys(CLOUDFLARE_AI_MODELS.textGeneration),
    textClassification: Object.keys(CLOUDFLARE_AI_MODELS.textClassification),
    textEmbeddings: Object.keys(CLOUDFLARE_AI_MODELS.textEmbeddings),
    imageClassification: Object.keys(CLOUDFLARE_AI_MODELS.imageClassification),
    speechRecognition: Object.keys(CLOUDFLARE_AI_MODELS.speechRecognition),
  };
}
