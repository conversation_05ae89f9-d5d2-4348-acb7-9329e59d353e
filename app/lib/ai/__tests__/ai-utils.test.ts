import { describe, expect, it, vi } from "vitest";
import {
  AI_ERROR_MESSAGES,
  getAIErrorMessage,
  isAPIKeyError,
  isRateLimitError,
  sanitizePromptForLogging,
  validateAIRequest,
} from "../ai-utils";

describe("AI Utils", () => {
  describe("validateAIRequest", () => {
    it("should validate correct AI requests", () => {
      const validRequest = {
        prompt: "Hello world",
        provider: "openai",
        model: "gpt-4o",
      };
      expect(validateAIRequest(validRequest)).toBe(true);
    });

    it("should reject requests with missing fields", () => {
      expect(validateAIRequest({})).toBe(false);
      expect(validateAIRequest({ prompt: "test" })).toBe(false);
      expect(validateAIRequest({ prompt: "test", provider: "openai" })).toBe(false);
    });

    it("should reject requests with empty prompt", () => {
      const request = {
        prompt: "",
        provider: "openai",
        model: "gpt-4o",
      };
      expect(validateAIRequest(request)).toBe(false);

      const requestWithSpaces = {
        prompt: "   ",
        provider: "openai",
        model: "gpt-4o",
      };
      expect(validateAIRequest(requestWithSpaces)).toBe(false);
    });

    it("should reject requests with invalid provider-model combinations", () => {
      const request = {
        prompt: "test",
        provider: "openai",
        model: "invalid-model",
      };
      expect(validateAIRequest(request)).toBe(false);
    });

    it("should reject non-object inputs", () => {
      expect(validateAIRequest(null)).toBe(false);
      expect(validateAIRequest(undefined)).toBe(false);
      expect(validateAIRequest("string")).toBe(false);
      expect(validateAIRequest(123)).toBe(false);
    });
  });

  describe("isAPIKeyError", () => {
    it("should identify API key errors", () => {
      expect(isAPIKeyError({ message: "Invalid API key" })).toBe(true);
      expect(isAPIKeyError({ message: "Unauthorized access" })).toBe(true);
      expect(isAPIKeyError({ message: "Authentication failed" })).toBe(true);
      expect(isAPIKeyError({ code: "invalid_api_key" })).toBe(true);
      expect(isAPIKeyError({ code: "unauthorized" })).toBe(true);
    });

    it("should not identify non-API key errors", () => {
      expect(isAPIKeyError({ message: "Rate limit exceeded" })).toBe(false);
      expect(isAPIKeyError({ message: "Server error" })).toBe(false);
      expect(isAPIKeyError({ code: "rate_limit" })).toBe(false);
      expect(isAPIKeyError(null)).toBe(false);
      expect(isAPIKeyError(undefined)).toBe(false);
    });
  });

  describe("isRateLimitError", () => {
    it("should identify rate limit errors", () => {
      expect(isRateLimitError({ message: "Rate limit exceeded" })).toBe(true);
      expect(isRateLimitError({ message: "Too many requests" })).toBe(true);
      expect(isRateLimitError({ code: "rate_limit_exceeded" })).toBe(true);
      expect(isRateLimitError({ code: "too_many_requests" })).toBe(true);
    });

    it("should not identify non-rate limit errors", () => {
      expect(isRateLimitError({ message: "Invalid API key" })).toBe(false);
      expect(isRateLimitError({ message: "Server error" })).toBe(false);
      expect(isRateLimitError({ code: "invalid_request" })).toBe(false);
      expect(isRateLimitError(null)).toBe(false);
      expect(isRateLimitError(undefined)).toBe(false);
    });
  });

  describe("getAIErrorMessage", () => {
    it("should return appropriate messages for different error types", () => {
      expect(getAIErrorMessage({ message: "Invalid API key" })).toBe(
        AI_ERROR_MESSAGES.MISSING_API_KEY
      );

      expect(getAIErrorMessage({ message: "Rate limit exceeded" })).toBe(
        AI_ERROR_MESSAGES.RATE_LIMIT_EXCEEDED
      );

      expect(getAIErrorMessage({ message: "Custom error message" })).toBe("Custom error message");
    });

    it("should return default message for unknown errors", () => {
      expect(getAIErrorMessage({})).toBe(AI_ERROR_MESSAGES.GENERATION_FAILED);
      expect(getAIErrorMessage(null)).toBe(AI_ERROR_MESSAGES.GENERATION_FAILED);
      expect(getAIErrorMessage(undefined)).toBe(AI_ERROR_MESSAGES.GENERATION_FAILED);
    });
  });

  describe("sanitizePromptForLogging", () => {
    it("should remove sensitive information", () => {
      const prompt = "My <NAME_EMAIL> and my card is 1234-5678-9012-3456";
      const sanitized = sanitizePromptForLogging(prompt);

      expect(sanitized).not.toContain("<EMAIL>");
      expect(sanitized).not.toContain("1234-5678-9012-3456");
      expect(sanitized).toContain("[EMAIL]");
      expect(sanitized).toContain("[CARD]");
    });

    it("should truncate long prompts", () => {
      const longPrompt = "a".repeat(200);
      const sanitized = sanitizePromptForLogging(longPrompt, 50);

      expect(sanitized.length).toBeLessThanOrEqual(53); // 50 + '...'
      expect(sanitized).toEndWith("...");
    });

    it("should handle empty or null prompts", () => {
      expect(sanitizePromptForLogging("")).toBe("");
      expect(sanitizePromptForLogging(null as any)).toBe("");
      expect(sanitizePromptForLogging(undefined as any)).toBe("");
    });

    it("should remove SSN patterns", () => {
      const prompt = "My SSN is ***********";
      const sanitized = sanitizePromptForLogging(prompt);

      expect(sanitized).not.toContain("***********");
      expect(sanitized).toContain("[SSN]");
    });
  });
});
