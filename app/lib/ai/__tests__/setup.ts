import { vi } from "vitest";

// Mock environment variables for testing
process.env.NODE_ENV = "test";

// Mock fetch globally
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
};

// Mock getUserUuid function
vi.mock("~/services/user", () => ({
  getUserUuid: vi.fn().mockResolvedValue("test-user-uuid-123"),
}));

// Mock database operations
vi.mock("~/services/credit", () => ({
  decreaseCredits: vi.fn().mockResolvedValue(true),
  increaseCredits: vi.fn().mockResolvedValue(true),
  getUserCredits: vi.fn().mockResolvedValue(100),
  hasEnoughCredits: vi.fn().mockResolvedValue(true),
  CreditsTransType: {
    Ping: "ping",
    Purchase: "purchase",
    Reward: "reward",
    Refund: "refund",
    AITextGeneration: "ai_text_generation",
    AIImageGeneration: "ai_image_generation",
    AIStreamText: "ai_stream_text",
  },
  CreditsAmount: {
    PingCost: 1,
    DefaultReward: 10,
    AITextGenerationCost: 5,
    AIImageGenerationCost: 10,
    AIStreamTextCost: 3,
  },
}));

// Mock AI providers
vi.mock("~/lib/ai/ai-providers", () => ({
  getAvailableProviders: vi.fn().mockReturnValue([
    {
      name: "openai",
      displayName: "OpenAI",
      models: ["gpt-4o", "gpt-4o-mini", "dall-e-3"],
    },
    {
      name: "deepseek",
      displayName: "DeepSeek",
      models: ["deepseek-chat", "deepseek-r1"],
    },
  ]),
  validateProviderModel: vi.fn().mockReturnValue(true),
}));

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
