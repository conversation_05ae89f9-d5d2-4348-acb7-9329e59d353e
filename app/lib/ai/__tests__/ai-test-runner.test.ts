import { beforeEach, describe, expect, it, vi } from "vitest";
import { DEFAULT_TEST_CASES, runTestCase, validateTestEnvironment } from "../ai-test-runner";
import type { TestCase } from "../ai-test-runner";

// Mock fetch globally
global.fetch = vi.fn();

describe("AI Test Runner", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("validateTestEnvironment", () => {
    it("should validate environment with all API keys present", () => {
      const envStatus = {
        OPENAI_API_KEY: true,
        DEEPSEEK_API_KEY: true,
        OPENROUTER_API_KEY: true,
        SILICONFLOW_API_KEY: true,
        REPLICATE_API_TOKEN: true,
      };

      const result = validateTestEnvironment(envStatus);

      expect(result.isValid).toBe(true);
      expect(result.missingKeys).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it("should identify missing required API keys", () => {
      const envStatus = {
        OPENAI_API_KEY: false,
        DEEPSEEK_API_KEY: false,
        OPENROUTER_API_KEY: true,
        SILICONFLOW_API_KEY: true,
        REPLICATE_API_TOKEN: true,
      };

      const result = validateTestEnvironment(envStatus);

      expect(result.isValid).toBe(false);
      expect(result.missingKeys).toContain("OPENAI_API_KEY");
      expect(result.missingKeys).toContain("DEEPSEEK_API_KEY");
    });

    it("should generate warnings for optional API keys", () => {
      const envStatus = {
        OPENAI_API_KEY: true,
        DEEPSEEK_API_KEY: true,
        OPENROUTER_API_KEY: false,
        SILICONFLOW_API_KEY: false,
        REPLICATE_API_TOKEN: false,
      };

      const result = validateTestEnvironment(envStatus);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(3);
      expect(result.warnings.some((w) => w.includes("OPENROUTER_API_KEY"))).toBe(true);
      expect(result.warnings.some((w) => w.includes("SILICONFLOW_API_KEY"))).toBe(true);
      expect(result.warnings.some((w) => w.includes("REPLICATE_API_TOKEN"))).toBe(true);
    });
  });

  describe("runTestCase", () => {
    it("should run a successful test case", async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          code: 0,
          data: { text: "Hello, world!" },
        }),
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const testCase: TestCase = {
        id: "test-1",
        name: "Test Case",
        description: "Test description",
        provider: "openai",
        model: "gpt-4o-mini",
        testType: "text",
        prompt: "Hello",
        shouldSucceed: true,
      };

      const result = await runTestCase(testCase, "http://localhost:3000");

      expect(result.success).toBe(true);
      expect(result.testCase).toBe(testCase);
      expect(result.duration).toBeGreaterThan(0);
      expect(result.response).toEqual({
        code: 0,
        data: { text: "Hello, world!" },
      });
      expect(result.timestamp).toBeDefined();
    });

    it("should handle failed test case", async () => {
      const mockResponse = {
        ok: false,
        json: vi.fn().mockResolvedValue({
          code: -1,
          message: "API Error",
        }),
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const testCase: TestCase = {
        id: "test-2",
        name: "Failing Test Case",
        description: "Test that should fail",
        provider: "openai",
        model: "invalid-model",
        testType: "text",
        prompt: "Hello",
        shouldSucceed: false,
      };

      const result = await runTestCase(testCase, "http://localhost:3000");

      expect(result.success).toBe(true); // Success because we expected failure
      expect(result.testCase).toBe(testCase);
      expect(result.response).toEqual({
        code: -1,
        message: "API Error",
      });
    });

    it("should handle network errors", async () => {
      (global.fetch as any).mockRejectedValue(new Error("Network error"));

      const testCase: TestCase = {
        id: "test-3",
        name: "Network Error Test",
        description: "Test network error handling",
        provider: "openai",
        model: "gpt-4o-mini",
        testType: "text",
        prompt: "Hello",
        shouldSucceed: false,
      };

      const result = await runTestCase(testCase, "http://localhost:3000");

      expect(result.success).toBe(true); // Success because we expected failure
      expect(result.error).toBe("Network error");
    });

    it("should build correct request for text generation", async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ code: 0, data: {} }),
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const testCase: TestCase = {
        id: "test-4",
        name: "Text Generation Test",
        description: "Test text generation request",
        provider: "deepseek",
        model: "deepseek-chat",
        testType: "text",
        prompt: "Test prompt",
        shouldSucceed: true,
      };

      await runTestCase(testCase, "http://localhost:3000");

      expect(global.fetch).toHaveBeenCalledWith("http://localhost:3000/api/ai/generate-text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: "Test prompt",
          provider: "deepseek",
          model: "deepseek-chat",
        }),
      });
    });

    it("should build correct request for image generation", async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ code: 0, data: {} }),
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const testCase: TestCase = {
        id: "test-5",
        name: "Image Generation Test",
        description: "Test image generation request",
        provider: "openai",
        model: "dall-e-3",
        testType: "image",
        prompt: "A beautiful sunset",
        shouldSucceed: true,
      };

      await runTestCase(testCase, "http://localhost:3000");

      expect(global.fetch).toHaveBeenCalledWith("http://localhost:3000/api/ai/generate-image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: "A beautiful sunset",
          provider: "openai",
          model: "dall-e-3",
          size: "1024x1024",
          n: 1,
        }),
      });
    });

    it("should build correct request for stream text", async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ code: 0, data: {} }),
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const testCase: TestCase = {
        id: "test-6",
        name: "Stream Text Test",
        description: "Test stream text request",
        provider: "openai",
        model: "gpt-4o",
        testType: "stream",
        prompt: "Tell me a story",
        shouldSucceed: true,
      };

      await runTestCase(testCase, "http://localhost:3000");

      expect(global.fetch).toHaveBeenCalledWith("http://localhost:3000/api/ai/stream-text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: "Tell me a story",
          provider: "openai",
          model: "gpt-4o",
        }),
      });
    });
  });

  describe("DEFAULT_TEST_CASES", () => {
    it("should have valid test cases", () => {
      expect(DEFAULT_TEST_CASES).toBeDefined();
      expect(Array.isArray(DEFAULT_TEST_CASES)).toBe(true);
      expect(DEFAULT_TEST_CASES.length).toBeGreaterThan(0);

      DEFAULT_TEST_CASES.forEach((testCase) => {
        expect(testCase).toHaveProperty("id");
        expect(testCase).toHaveProperty("name");
        expect(testCase).toHaveProperty("description");
        expect(testCase).toHaveProperty("provider");
        expect(testCase).toHaveProperty("model");
        expect(testCase).toHaveProperty("testType");
        expect(testCase).toHaveProperty("prompt");
        expect(testCase).toHaveProperty("shouldSucceed");

        expect(typeof testCase.id).toBe("string");
        expect(typeof testCase.name).toBe("string");
        expect(typeof testCase.description).toBe("string");
        expect(typeof testCase.provider).toBe("string");
        expect(typeof testCase.model).toBe("string");
        expect(["text", "stream", "image"]).toContain(testCase.testType);
        expect(typeof testCase.prompt).toBe("string");
        expect(typeof testCase.shouldSucceed).toBe("boolean");
      });
    });

    it("should include tests for all major providers", () => {
      const providers = [...new Set(DEFAULT_TEST_CASES.map((tc) => tc.provider))];

      expect(providers).toContain("openai");
      expect(providers).toContain("deepseek");
      expect(providers).toContain("openrouter");
      expect(providers).toContain("siliconflow");
      expect(providers).toContain("replicate");
    });

    it("should include tests for all test types", () => {
      const testTypes = [...new Set(DEFAULT_TEST_CASES.map((tc) => tc.testType))];

      expect(testTypes).toContain("text");
      expect(testTypes).toContain("stream");
      expect(testTypes).toContain("image");
    });

    it("should include both success and failure test cases", () => {
      const shouldSucceedValues = [...new Set(DEFAULT_TEST_CASES.map((tc) => tc.shouldSucceed))];

      expect(shouldSucceedValues).toContain(true);
      expect(shouldSucceedValues).toContain(false);
    });
  });
});
