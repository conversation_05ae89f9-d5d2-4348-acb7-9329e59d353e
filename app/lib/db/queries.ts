// Specialized database queries for complex data retrieval
import { and, asc, count, desc, eq, gte, inArray, like, lte, or, sql } from "drizzle-orm";
import type { Database } from "./db";
import type { FilterOptions, PaginationOptions, PaginationResult } from "./operations";
import { calculatePagination } from "./operations";
import * as schema from "./schema";

// Advanced user queries
export const userQueries = {
  // Search users with filters
  async searchUsers(
    db: Database,
    options: PaginationOptions &
      FilterOptions & {
        email?: string;
        name?: string;
        isAffiliate?: boolean;
        minCredits?: number;
        maxCredits?: number;
      } = {}
  ): Promise<PaginationResult<schema.User>> {
    const {
      page = 1,
      limit = 10,
      search,
      email,
      name,
      isAffiliate,
      minCredits,
      maxCredits,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(like(schema.users.name, `%${search}%`), like(schema.users.email, `%${search}%`))
      );
    }

    if (email) {
      conditions.push(eq(schema.users.email, email));
    }

    if (name) {
      conditions.push(like(schema.users.name, `%${name}%`));
    }

    if (typeof isAffiliate === "boolean") {
      conditions.push(eq(schema.users.isAffiliate, isAffiliate));
    }

    if (typeof minCredits === "number") {
      conditions.push(gte(schema.users.credits, minCredits));
    }

    if (typeof maxCredits === "number") {
      conditions.push(lte(schema.users.credits, maxCredits));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [totalResult] = await db.select({ count: count() }).from(schema.users).where(whereClause);
    const total = totalResult?.count || 0;

    // Build order by clause
    let orderByClause;
    switch (sortBy) {
      case "name":
        orderByClause = sortOrder === "desc" ? desc(schema.users.name) : asc(schema.users.name);
        break;
      case "email":
        orderByClause = sortOrder === "desc" ? desc(schema.users.email) : asc(schema.users.email);
        break;
      case "credits":
        orderByClause =
          sortOrder === "desc" ? desc(schema.users.credits) : asc(schema.users.credits);
        break;
      default:
        orderByClause =
          sortOrder === "desc" ? desc(schema.users.createdAt) : asc(schema.users.createdAt);
    }

    // Get paginated data
    const data = await db
      .select()
      .from(schema.users)
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    return {
      data,
      pagination: calculatePagination(page, limit, total),
    };
  },

  // Get user with all related data
  async getUserWithRelations(db: Database, userId: string) {
    const user = await db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);

    if (!user[0]) return null;

    // Get user's accounts
    const accounts = await db
      .select({
        account: schema.accounts,
        membership: schema.accountsMemberships,
        role: schema.roles,
      })
      .from(schema.accountsMemberships)
      .innerJoin(schema.accounts, eq(schema.accountsMemberships.accountId, schema.accounts.id))
      .leftJoin(schema.roles, eq(schema.accountsMemberships.accountRole, schema.roles.name))
      .where(eq(schema.accountsMemberships.userId, userId));

    // Get user's recent orders
    const recentOrders = await db
      .select()
      .from(schema.orders)
      .where(eq(schema.orders.userUuid, user[0].uuid))
      .orderBy(desc(schema.orders.createdAt))
      .limit(5);

    // Get user's credit balance and recent transactions
    const recentCreditTransactions = await db
      .select()
      .from(schema.creditTransactions)
      .where(eq(schema.creditTransactions.userUuid, user[0].uuid))
      .orderBy(desc(schema.creditTransactions.createdAt))
      .limit(10);

    return {
      user: user[0],
      accounts,
      recentOrders,
      recentCreditTransactions,
    };
  },
};

// Advanced order queries
export const orderQueries = {
  // Search orders with advanced filters
  async searchOrders(
    db: Database,
    options: PaginationOptions &
      FilterOptions & {
        userUuid?: string;
        accountId?: string;
        status?: string[];
        minAmount?: number;
        maxAmount?: number;
        dateFrom?: Date;
        dateTo?: Date;
        billingProvider?: string[];
      } = {}
  ): Promise<PaginationResult<schema.Order>> {
    const {
      page = 1,
      limit = 10,
      search,
      userUuid,
      accountId,
      status,
      minAmount,
      maxAmount,
      dateFrom,
      dateTo,
      billingProvider,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(like(schema.orders.orderNo, `%${search}%`), like(schema.orders.userEmail, `%${search}%`))
      );
    }

    if (userUuid) {
      conditions.push(eq(schema.orders.userUuid, userUuid));
    }

    if (accountId) {
      conditions.push(eq(schema.orders.accountId, accountId));
    }

    if (status && status.length > 0) {
      conditions.push(inArray(schema.orders.status, status));
    }

    if (typeof minAmount === "number") {
      conditions.push(gte(schema.orders.totalAmount, minAmount.toString()));
    }

    if (typeof maxAmount === "number") {
      conditions.push(lte(schema.orders.totalAmount, maxAmount.toString()));
    }

    if (dateFrom) {
      conditions.push(gte(schema.orders.createdAt, dateFrom));
    }

    if (dateTo) {
      conditions.push(lte(schema.orders.createdAt, dateTo));
    }

    if (billingProvider && billingProvider.length > 0) {
      conditions.push(inArray(schema.orders.billingProvider, billingProvider));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(schema.orders)
      .where(whereClause);
    const total = totalResult?.count || 0;

    // Build order by clause
    let orderByClause;
    switch (sortBy) {
      case "orderNo":
        orderByClause =
          sortOrder === "desc" ? desc(schema.orders.orderNo) : asc(schema.orders.orderNo);
        break;
      case "totalAmount":
        orderByClause =
          sortOrder === "desc" ? desc(schema.orders.totalAmount) : asc(schema.orders.totalAmount);
        break;
      case "status":
        orderByClause =
          sortOrder === "desc" ? desc(schema.orders.status) : asc(schema.orders.status);
        break;
      default:
        orderByClause =
          sortOrder === "desc" ? desc(schema.orders.createdAt) : asc(schema.orders.createdAt);
    }

    // Get paginated data
    const data = await db
      .select()
      .from(schema.orders)
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    return {
      data,
      pagination: calculatePagination(page, limit, total),
    };
  },

  // Get order analytics
  async getOrderAnalytics(
    db: Database,
    options: {
      dateFrom?: Date;
      dateTo?: Date;
      accountId?: string;
    } = {}
  ) {
    const { dateFrom, dateTo, accountId } = options;

    const conditions = [];
    if (dateFrom) conditions.push(gte(schema.orders.createdAt, dateFrom));
    if (dateTo) conditions.push(lte(schema.orders.createdAt, dateTo));
    if (accountId) conditions.push(eq(schema.orders.accountId, accountId));

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get order statistics
    const [stats] = await db
      .select({
        totalOrders: count(),
        totalRevenue: sql<number>`SUM(CAST(${schema.orders.totalAmount} AS DECIMAL))`,
        avgOrderValue: sql<number>`AVG(CAST(${schema.orders.totalAmount} AS DECIMAL))`,
      })
      .from(schema.orders)
      .where(whereClause);

    // Get orders by status
    const ordersByStatus = await db
      .select({
        status: schema.orders.status,
        count: count(),
        totalAmount: sql<number>`SUM(CAST(${schema.orders.totalAmount} AS DECIMAL))`,
      })
      .from(schema.orders)
      .where(whereClause)
      .groupBy(schema.orders.status);

    // Get orders by provider
    const ordersByProvider = await db
      .select({
        provider: schema.orders.billingProvider,
        count: count(),
        totalAmount: sql<number>`SUM(CAST(${schema.orders.totalAmount} AS DECIMAL))`,
      })
      .from(schema.orders)
      .where(whereClause)
      .groupBy(schema.orders.billingProvider);

    return {
      stats,
      ordersByStatus,
      ordersByProvider,
    };
  },
};

// Dashboard queries
export const dashboardQueries = {
  // Get dashboard overview
  async getOverview(db: Database, accountId?: string) {
    const conditions = accountId ? [eq(schema.orders.accountId, accountId)] : [];
    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get counts
    const [userCount] = await db.select({ count: count() }).from(schema.users);
    const [orderCount] = await db.select({ count: count() }).from(schema.orders).where(whereClause);
    const [accountCount] = await db.select({ count: count() }).from(schema.accounts);

    // Get recent activity
    const recentOrders = await db
      .select()
      .from(schema.orders)
      .where(whereClause)
      .orderBy(desc(schema.orders.createdAt))
      .limit(5);

    const recentUsers = await db
      .select()
      .from(schema.users)
      .orderBy(desc(schema.users.createdAt))
      .limit(5);

    return {
      counts: {
        users: userCount.count,
        orders: orderCount.count,
        accounts: accountCount.count,
      },
      recentOrders,
      recentUsers,
    };
  },
};

// Export all query collections
export const dbQueries = {
  user: userQueries,
  order: orderQueries,
  dashboard: dashboardQueries,
};
