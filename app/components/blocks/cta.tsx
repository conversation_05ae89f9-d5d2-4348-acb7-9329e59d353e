import { Link } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import ContentSection from "./content-section";

interface CTAButton {
  title: string;
  url: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
}

interface CTAProps {
  title: string;
  description: string;
  buttons: CTAButton[];
}

export default function CTA({ title, description, buttons }: CTAProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="cta"
      decorations={true}
      padding="lg"
      headerSpacing="md"
      maxWidth="6xl"
    >
      {/* Decorative elements */}
      <div className="flex items-center justify-center gap-4 mb-16">
        <div className="w-16 h-0.5 bg-gradient-to-r from-transparent via-blue-500 to-purple-500" />
        <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse" />
        <div className="w-16 h-0.5 bg-gradient-to-r from-purple-500 via-cyan-500 to-transparent" />
      </div>

      {/* Enhanced Buttons */}
      <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
        {buttons.map((button, index) => (
          <div key={index} className="group relative">
            {index === 0 && (
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000 group-hover:duration-200" />
            )}
            <Button
              asChild
              size="lg"
              variant={button.variant || "default"}
              className={`
                relative min-w-[240px] h-14 text-lg font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl
                ${
                  index === 0
                    ? "bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 hover:from-blue-700 hover:via-purple-700 hover:to-cyan-700 text-white border-0 shadow-2xl hover:shadow-blue-500/40"
                    : "border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 shadow-xl hover:shadow-2xl"
                }
              `}
            >
              <Link to={button.url} className="flex items-center gap-3">
                <span>{button.title}</span>
                {index === 0 && (
                  <div className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse" />
                    <svg
                      className="w-5 h-5 transition-transform group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      />
                    </svg>
                  </div>
                )}
              </Link>
            </Button>
            {index === 0 && (
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            )}
          </div>
        ))}
      </div>

      {/* Enhanced Additional info */}
      <div className="text-center space-y-6">
        <div className="flex items-center justify-center gap-3 text-lg font-medium text-gray-600 dark:text-gray-300">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span>Join 10,000+ developers building amazing SaaS applications</span>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse delay-500" />
        </div>

        {/* Trust indicators */}
        <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground flex-wrap">
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <span>No credit card required</span>
          </div>
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <span>Enterprise security</span>
          </div>
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            <span>Deploy in minutes</span>
          </div>
        </div>
      </div>
    </ContentSection>
  );
}
