import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface Testimonial {
  name: string;
  role: string;
  content: string;
  avatar?: string;
}

interface TestimonialsProps {
  title: string;
  description: string;
  items: Testimonial[];
}

export default function Testimonials({ title, description, items }: TestimonialsProps) {
  // Transform testimonials to CardItem format
  const cardItems = items.map((testimonial) => ({
    title: testimonial.name,
    description: testimonial.role,
    content: testimonial.content,
    name: testimonial.name,
    role: testimonial.role,
    avatar: testimonial.avatar,
  }));

  return (
    <ContentSection
      title={title}
      description={description}
      background="testimonials"
      decorations={true}
      padding="md"
      headerSpacing="md"
    >
      <CardGrid items={cardItems} columns={3} variant="testimonial" animationDelay={0.1} />
    </ContentSection>
  );
}
