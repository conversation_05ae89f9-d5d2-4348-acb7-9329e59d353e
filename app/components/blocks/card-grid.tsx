import { Badge } from "~/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

interface CardItem {
  icon?: React.ReactNode | string;
  title: string;
  description: string;
  image?: string;
  url?: string;
  // For testimonials
  name?: string;
  role?: string;
  content?: string;
  avatar?: string;
  // For stats/metrics
  label?: string;
  value?: string;
  trend?: "up" | "down" | "stable";
  color?: "green" | "blue" | "purple" | "orange" | "rose" | "emerald";
}

interface CardGridProps {
  items: CardItem[];
  columns?: 1 | 2 | 3 | 4;
  variant?: "default" | "feature" | "showcase" | "testimonial" | "stats" | "metric" | "value";
  animationDelay?: number;
}

export default function CardGrid({
  items,
  columns = 3,
  variant = "default",
  animationDelay = 0.1,
}: CardGridProps) {
  const getGridClass = () => {
    switch (columns) {
      case 1:
        return "grid-cols-1";
      case 2:
        return "grid-cols-1 md:grid-cols-2";
      case 4:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
      default:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
    }
  };

  const getColorClasses = (color = "blue") => {
    const colorMap = {
      green: {
        bg: "from-green-500 to-emerald-500",
        text: "text-green-600 dark:text-green-400",
        badge: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
        shadow: "hover:shadow-green-500/20 dark:hover:shadow-green-400/20",
      },
      blue: {
        bg: "from-blue-500 to-cyan-500",
        text: "text-blue-600 dark:text-blue-400",
        badge: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
        shadow: "hover:shadow-blue-500/20 dark:hover:shadow-blue-400/20",
      },
      purple: {
        bg: "from-purple-500 to-violet-500",
        text: "text-purple-600 dark:text-purple-400",
        badge: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
        shadow: "hover:shadow-purple-500/20 dark:hover:shadow-purple-400/20",
      },
      orange: {
        bg: "from-orange-500 to-red-500",
        text: "text-orange-600 dark:text-orange-400",
        badge: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
        shadow: "hover:shadow-orange-500/20 dark:hover:shadow-orange-400/20",
      },
      rose: {
        bg: "from-rose-500 to-pink-500",
        text: "text-rose-600 dark:text-rose-400",
        badge: "bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-200",
        shadow: "hover:shadow-rose-500/20 dark:hover:shadow-rose-400/20",
      },
      emerald: {
        bg: "from-emerald-500 to-teal-500",
        text: "text-emerald-600 dark:text-emerald-400",
        badge: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200",
        shadow: "hover:shadow-emerald-500/20 dark:hover:shadow-emerald-400/20",
      },
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const renderCard = (item: CardItem, index: number) => {
    const colorClasses = getColorClasses(item.color);

    switch (variant) {
      case "showcase":
        return (
          <Card
            key={index}
            className="group overflow-hidden border border-white/20 dark:border-gray-700/50 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-gray-800/90 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20 dark:hover:shadow-purple-400/20 card-hover"
            style={{ animationDelay: `${index * animationDelay}s` }}
          >
            {/* Image container */}
            <div className="relative aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 overflow-hidden">
              {item.image ? (
                <>
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center text-3xl text-white shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300">
                    📷
                  </div>
                </div>
              )}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent" />
            </div>

            <CardHeader className="pb-3">
              <CardTitle className="text-xl font-semibold group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                {item.title}
              </CardTitle>
            </CardHeader>

            <CardContent className="pt-0">
              <CardDescription className="text-base leading-relaxed text-muted-foreground">
                {item.description}
              </CardDescription>
            </CardContent>

            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
          </Card>
        );

      case "testimonial":
        return (
          <Card
            key={index}
            className="group relative border border-white/20 dark:border-gray-700/50 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-gray-800/90 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-rose-500/20 dark:hover:shadow-rose-400/20 card-hover overflow-hidden"
            style={{ animationDelay: `${index * animationDelay}s` }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-rose-500/20 via-pink-500/20 to-violet-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="absolute inset-[1px] bg-white/90 dark:bg-gray-800/90 rounded-lg" />

            <CardContent className="p-8 relative z-10">
              <div className="mb-8">
                <div className="relative mb-4">
                  <div className="text-6xl font-serif text-rose-500/20 leading-none select-none group-hover:text-rose-500/40 transition-colors duration-300">
                    "
                  </div>
                </div>

                <p className="text-base leading-relaxed italic text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">
                  {item.content || item.description}
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <div className="relative">
                  {item.avatar ? (
                    <img
                      src={item.avatar}
                      alt={item.name || item.title}
                      className="w-14 h-14 rounded-full object-cover ring-2 ring-rose-500/20 group-hover:ring-rose-500/40 group-hover:scale-110 transition-all duration-300"
                    />
                  ) : (
                    <div className="w-14 h-14 bg-gradient-to-br from-rose-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:scale-110 group-hover:shadow-rose-500/25 transition-all duration-300">
                      {(item.name || item.title).charAt(0)}
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-br from-rose-500/20 to-pink-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                <div className="flex-1">
                  <div className="font-semibold text-foreground group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors duration-300">
                    {item.name || item.title}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {item.role || item.description}
                  </div>
                </div>
              </div>
            </CardContent>

            <div className="absolute top-4 right-4 w-2 h-2 bg-rose-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-500" />
            <div
              className="absolute bottom-4 left-4 w-1 h-1 bg-pink-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-700"
              style={{ animationDelay: "0.3s" }}
            />
          </Card>
        );

      case "stats":
        return (
          <div
            key={index}
            className="group relative text-center p-8 rounded-2xl border border-white/20 dark:border-gray-700/50 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-gray-800/90 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-emerald-500/20 dark:hover:shadow-emerald-400/20 card-hover"
            style={{ animationDelay: `${index * animationDelay}s` }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 via-teal-500/20 to-blue-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="absolute inset-[1px] bg-white/90 dark:bg-gray-800/90 rounded-2xl" />

            <div className="relative z-10 space-y-4">
              <div className="relative">
                <div className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 dark:from-emerald-400 dark:to-teal-400 bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                  {item.label || item.value}
                </div>
                <div className="absolute inset-0 text-5xl lg:text-6xl font-bold text-emerald-500/20 blur-sm group-hover:text-emerald-500/40 transition-colors duration-300">
                  {item.label || item.value}
                </div>
              </div>

              <div className="text-2xl font-semibold text-foreground group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors duration-300">
                {item.title}
              </div>

              <div className="text-base text-muted-foreground leading-relaxed">
                {item.description}
              </div>
            </div>

            <div className="absolute top-4 right-4 w-2 h-2 bg-emerald-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-float transition-all duration-500" />
            <div
              className="absolute bottom-4 left-4 w-1 h-1 bg-teal-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-float transition-all duration-700"
              style={{ animationDelay: "0.2s" }}
            />
          </div>
        );

      case "metric":
        return (
          <Card
            key={index}
            className={`group relative overflow-hidden border-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-900/80 transition-all duration-300 hover:scale-105 hover:shadow-2xl ${colorClasses.shadow}`}
          >
            <div
              className={`absolute inset-0 bg-gradient-to-r ${colorClasses.bg} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
            />

            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div
                  className={`w-12 h-12 rounded-xl bg-gradient-to-r ${colorClasses.bg} flex items-center justify-center text-white text-xl group-hover:scale-110 transition-transform duration-300`}
                >
                  {item.icon}
                </div>

                {item.trend && (
                  <Badge variant="secondary" className={`${colorClasses.badge} border-0`}>
                    {item.trend === "up" && "↗️"}
                    {item.trend === "down" && "↘️"}
                    {item.trend === "stable" && "➡️"}
                  </Badge>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-2">
              <div className={`text-3xl font-bold ${colorClasses.text}`}>{item.value}</div>
              <CardTitle className="text-lg font-semibold text-foreground">{item.title}</CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {item.description}
              </CardDescription>
            </CardContent>

            <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </Card>
        );

      default:
        return (
          <Card
            key={index}
            className="group relative overflow-hidden border border-white/20 dark:border-gray-700/50 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-gray-800/90 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20 dark:hover:shadow-blue-400/20 card-hover"
            style={{ animationDelay: `${index * animationDelay}s` }}
          >
            {(variant === "feature" || variant === "value") && (
              <>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="absolute inset-[1px] bg-white/90 dark:bg-gray-800/90 rounded-lg" />
              </>
            )}

            <CardHeader className="text-center relative z-10 pb-4">
              {item.icon && (
                <div className="mx-auto mb-6 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                  {typeof item.icon === "string" && item.icon.startsWith("/") ? (
                    <img src={item.icon} alt={item.title} className="w-10 h-10" />
                  ) : (
                    <span className="text-3xl text-white">{item.icon}</span>
                  )}
                </div>
              )}
              <CardTitle className="text-xl font-semibold group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {item.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="relative z-10">
              <CardDescription className="text-center text-base leading-relaxed text-muted-foreground">
                {item.description}
              </CardDescription>
              {item.image && (
                <div className="mt-6 rounded-xl overflow-hidden shadow-lg">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
              )}
            </CardContent>

            {(variant === "feature" || variant === "value") && (
              <div className="absolute inset-0 bg-gradient-to-t from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg" />
            )}
          </Card>
        );
    }
  };

  return (
    <div className={`grid ${getGridClass()} gap-8`}>
      {items.map((item, index) => renderCard(item, index))}
    </div>
  );
}
