import ContentSection from "./content-section";

interface PageHeaderProps {
  badge?: {
    text: string;
    icon?: string;
  };
  title: string;
  description: string;
  children?: React.ReactNode;
}

export default function PageHeader({ badge, title, description, children }: PageHeaderProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="gradient"
      decorations={true}
      padding="xl"
      headerSpacing="lg"
    >
      {/* Badge */}
      {badge && (
        <div className="flex items-center justify-center mb-12">
          <div className="group relative">
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-full blur opacity-25 group-hover:opacity-40 transition duration-1000" />
            <div className="relative h-12 px-6 py-3 bg-white dark:bg-gray-900 border border-blue-500/20 rounded-full shadow-lg flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent">
                {badge.icon && badge.icon} {badge.text}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Additional content */}
      {children}
    </ContentSection>
  );
}
