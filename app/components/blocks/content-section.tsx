interface ContentSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  background?:
    | "default"
    | "muted"
    | "gradient"
    | "showcase"
    | "testimonials"
    | "stats"
    | "features"
    | "cta";
  decorations?: boolean;
  padding?: "sm" | "md" | "lg" | "xl";
  headerSpacing?: "sm" | "md" | "lg";
  maxWidth?: "4xl" | "5xl" | "6xl" | "7xl";
}

export default function ContentSection({
  title,
  description,
  children,
  className = "",
  background = "default",
  decorations = true,
  padding = "lg",
  headerSpacing = "lg",
  maxWidth = "7xl",
}: ContentSectionProps) {
  const getBackgroundClass = () => {
    switch (background) {
      case "muted":
        return "bg-gradient-to-b from-muted/20 via-background to-muted/30";
      case "gradient":
        return "bg-gradient-to-b from-background to-muted/20";
      case "showcase":
        return "bg-gradient-to-b from-muted/20 via-background to-muted/30";
      case "testimonials":
        return "bg-gradient-to-b from-muted/20 to-background";
      case "stats":
        return "bg-gradient-to-b from-background to-muted/20";
      case "features":
        return "bg-gradient-to-b from-background to-muted/30";
      case "cta":
        return "bg-gradient-to-b from-background via-muted/20 to-background";
      default:
        return "bg-background";
    }
  };

  const getPaddingClass = () => {
    switch (padding) {
      case "sm":
        return "py-16";
      case "md":
        return "py-24";
      case "xl":
        return "py-40";
      default:
        return "py-32";
    }
  };

  const getHeaderSpacingClass = () => {
    switch (headerSpacing) {
      case "sm":
        return "mb-12";
      case "md":
        return "mb-16";
      case "lg":
        return "mb-24";
      default:
        return "mb-20";
    }
  };

  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case "4xl":
        return "max-w-4xl";
      case "5xl":
        return "max-w-5xl";
      case "6xl":
        return "max-w-6xl";
      default:
        return "max-w-7xl";
    }
  };

  const getDecorationsByBackground = () => {
    switch (background) {
      case "showcase":
        return (
          <>
            <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-l from-cyan-400/15 to-blue-400/15 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-gradient-to-r from-purple-400/15 to-pink-400/15 rounded-full blur-3xl animate-pulse delay-1000" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-green-400/10 to-emerald-400/10 rounded-full blur-3xl animate-pulse delay-2000" />
            <div className="absolute inset-0 bg-grid-pattern opacity-[0.02]" />
            <div className="absolute top-20 left-1/4 w-2 h-2 bg-blue-400/60 rounded-full animate-float" />
            <div className="absolute bottom-20 right-1/4 w-3 h-3 bg-purple-400/60 rounded-full animate-float delay-1000" />
            <div className="absolute top-1/2 right-10 w-1 h-1 bg-cyan-400/60 rounded-full animate-float delay-2000" />
          </>
        );
      case "testimonials":
        return (
          <>
            <div className="absolute top-1/4 left-0 w-96 h-96 bg-gradient-to-r from-rose-400/10 to-pink-400/10 rounded-full blur-3xl" />
            <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-gradient-to-l from-violet-400/10 to-purple-400/10 rounded-full blur-3xl" />
            <div className="absolute inset-0 bg-grid-pattern opacity-5" />
          </>
        );
      case "stats":
        return (
          <>
            <div className="absolute top-0 left-1/3 w-96 h-96 bg-gradient-to-r from-emerald-400/10 to-teal-400/10 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/3 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-indigo-400/10 rounded-full blur-3xl" />
            <div className="absolute inset-0 bg-grid-pattern opacity-5" />
          </>
        );
      case "features":
        return (
          <>
            <div className="absolute inset-0 bg-grid-pattern opacity-5" />
            <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl" />
          </>
        );
      case "cta":
        return (
          <>
            <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-gradient-to-r from-blue-400/15 to-purple-400/15 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-l from-purple-400/15 to-pink-400/15 rounded-full blur-3xl animate-pulse delay-1000" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-gradient-to-r from-cyan-400/10 to-emerald-400/10 rounded-full blur-3xl animate-pulse delay-2000" />
            <div className="absolute inset-0 bg-grid-pattern opacity-[0.02]" />
            <div className="absolute top-20 right-20 w-4 h-4 bg-blue-400/40 rounded-full animate-bounce delay-500" />
            <div className="absolute bottom-20 left-20 w-3 h-3 bg-purple-400/40 rounded-full animate-bounce delay-1000" />
            <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-cyan-400/40 rounded-full animate-bounce delay-1500" />
          </>
        );
      default:
        return (
          <>
            <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-l from-cyan-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
            <div className="absolute inset-0 bg-grid-pattern opacity-[0.02]" />
          </>
        );
    }
  };

  return (
    <section
      className={`${getPaddingClass()} ${getBackgroundClass()} relative overflow-hidden ${className}`}
    >
      {/* Background decorations */}
      {decorations && <div className="absolute inset-0">{getDecorationsByBackground()}</div>}

      <div className={`${getMaxWidthClass()} mx-auto px-4 relative z-10`}>
        {/* Header */}
        {(title || description) && (
          <div className={`text-center ${getHeaderSpacingClass()}`}>
            {title && (
              <div className="relative">
                <h2 className="text-4xl font-bold lg:text-5xl bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight mb-6">
                  {title}
                </h2>
                <div className="absolute -inset-4 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-cyan-600/10 blur-2xl -z-10 animate-pulse" />
              </div>
            )}
            {description && (
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                {description}
              </p>
            )}
          </div>
        )}

        {/* Content */}
        {children}
      </div>
    </section>
  );
}
