import { useSearchParams } from "@remix-run/react";
import { useEffect, useState } from "react";
import type { FilterValue } from "~/types/common";

export interface FilterField {
  key: string;
  label: string;
  type: "text" | "select" | "number" | "date" | "multiselect";
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  min?: number;
  max?: number;
}

export interface SearchFilterProps {
  fields: FilterField[];
  onFilterChange?: (filters: FilterValue) => void;
  showSearch?: boolean;
  searchPlaceholder?: string;
  className?: string;
}

export function SearchFilter({
  fields,
  onFilterChange,
  showSearch = true,
  searchPlaceholder = "Search...",
  className = "",
}: SearchFilterProps) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState<FilterValue>({});
  const [searchTerm, setSearchTerm] = useState("");

  // Initialize filters from URL params
  useEffect(() => {
    const initialFilters: FilterValue = {};

    if (showSearch) {
      initialFilters.search = searchParams.get("search") || "";
      setSearchTerm(initialFilters.search);
    }

    fields.forEach((field) => {
      const value = searchParams.get(field.key);
      if (value) {
        if (field.type === "multiselect") {
          initialFilters[field.key] = value.split(",");
        } else if (field.type === "number") {
          initialFilters[field.key] = parseFloat(value);
        } else {
          initialFilters[field.key] = value;
        }
      }
    });

    setFilters(initialFilters);
  }, [searchParams, fields, showSearch]);

  const handleFilterChange = (key: string, value: string | number | boolean | string[]) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    const newFilters = { ...filters, search: value };
    setFilters(newFilters);
  };

  const applyFilters = () => {
    const newParams = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            newParams.set(key, value.join(","));
          }
        } else {
          newParams.set(key, value.toString());
        }
      }
    });

    newParams.set("page", "1"); // Reset to first page
    setSearchParams(newParams);

    if (onFilterChange) {
      onFilterChange(filters);
    }
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm("");
    setSearchParams(new URLSearchParams());

    if (onFilterChange) {
      onFilterChange({});
    }
  };

  const renderField = (field: FilterField) => {
    const value = filters[field.key] || "";
    const fieldId = `filter-${field.key}`;

    switch (field.type) {
      case "text":
        return (
          <input
            id={fieldId}
            type="text"
            value={typeof value === "string" ? value : ""}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            placeholder={field.placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );

      case "number":
        return (
          <input
            id={fieldId}
            type="number"
            value={typeof value === "number" ? value : ""}
            onChange={(e) =>
              handleFilterChange(field.key, e.target.value ? parseFloat(e.target.value) : "")
            }
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );

      case "date":
        return (
          <input
            id={fieldId}
            type="date"
            value={typeof value === "string" ? value : ""}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );

      case "select":
        return (
          <select
            id={fieldId}
            value={typeof value === "string" ? value : ""}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{field.placeholder || `Select ${field.label}`}</option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case "multiselect":
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="checkbox"
                  checked={Array.isArray(value) && value.includes(option.value)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(value) ? value : [];
                    if (e.target.checked) {
                      handleFilterChange(field.key, [...currentValues, option.value]);
                    } else {
                      handleFilterChange(
                        field.key,
                        currentValues.filter((v) => v !== option.value)
                      );
                    }
                  }}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          <button
            type="button"
            onClick={clearFilters}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Clear All
          </button>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          {/* Search Field */}
          {showSearch && (
            <div>
              <label
                htmlFor="search-input"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Search
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  id="search-input"
                  type="text"
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder={searchPlaceholder}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}

          {/* Filter Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {fields.map((field) => (
              <div key={field.key}>
                <label
                  htmlFor={`filter-${field.key}`}
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  {field.label}
                </label>
                {renderField(field)}
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={clearFilters}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Clear
          </button>
          <button
            type="button"
            onClick={applyFilters}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}
