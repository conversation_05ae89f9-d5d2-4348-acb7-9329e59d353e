// app/components/zustand-provider.tsx
import React, { type ReactNode, useEffect } from "react";
import { useAppStore, useCartStore, useUIStore, useUserStore } from "~/stores";

interface ZustandProviderProps {
  children: ReactNode;
}

/**
 * Zustand Provider 组件
 * 处理客户端水合和初始化逻辑
 */
export function ZustandProvider({ children }: ZustandProviderProps) {
  const setInitialized = useAppStore((state) => state.setInitialized);
  const setOnline = useAppStore((state) => state.setOnline);
  const [isHydrated, setIsHydrated] = React.useState(false);

  useEffect(() => {
    // 水合持久化的 stores
    const hydrateStores = async () => {
      try {
        // 手动触发持久化 stores 的水合
        if (typeof window !== "undefined") {
          await Promise.all([
            useUserStore.persist?.rehydrate(),
            useUIStore.persist?.rehydrate(),
            useCartStore.persist?.rehydrate(),
          ]);
        }
      } catch (error) {
        console.warn("Failed to rehydrate stores:", error);
      } finally {
        // 标记应用已初始化
        setInitialized(true);
        setIsHydrated(true);
      }
    };

    hydrateStores();
  }, [setInitialized]);

  useEffect(() => {
    // 监听网络状态
    const handleOnline = () => setOnline(true);
    const handleOffline = () => setOnline(false);

    if (typeof window !== "undefined") {
      window.addEventListener("online", handleOnline);
      window.addEventListener("offline", handleOffline);

      // 设置初始网络状态
      setOnline(navigator.onLine);

      return () => {
        window.removeEventListener("online", handleOnline);
        window.removeEventListener("offline", handleOffline);
      };
    }
  }, [setOnline]);

  // Prevent hydration mismatch by not rendering children until hydrated
  if (!isHydrated) {
    return null;
  }

  return <>{children}</>;
}
