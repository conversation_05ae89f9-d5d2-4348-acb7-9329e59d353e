/**
 * 极简 Google One Tap 组件
 * 自动弹窗 + 备用按钮
 */

import { useEffect } from "react";

// Google One Tap 类型定义
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: GoogleOneTapConfig) => void;
          prompt: (callback?: (notification: any) => void) => void;
          renderButton: (parent: HTMLElement, options: any) => void;
        };
      };
    };
  }
}

interface GoogleOneTapConfig {
  client_id: string;
  callback: (response: { credential: string }) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
}

interface GoogleOneTapProps {
  clientId?: string;
  enabled?: boolean;
}

export function GoogleOneTap({ clientId, enabled = true }: GoogleOneTapProps) {
  useEffect(() => {
    if (!enabled || !clientId) return;

    // 加载 Google One Tap 脚本
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      // 初始化 Google One Tap
      window.google?.accounts.id.initialize({
        client_id: clientId,
        callback: ({ credential }) => {
          // 设置 Cookie 并刷新页面
          document.cookie = `g_credential=${credential}; Path=/; Secure; SameSite=Lax`;
          window.location.reload();
        },
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      // 显示 One Tap 弹窗
      window.google?.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed()) {
          console.log("One Tap not displayed:", notification.getNotDisplayedReason());
        }
      });
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [clientId, enabled]);

  // 不渲染任何 UI，Google 会自动插入 DOM
  return null;
}

/**
 * Neon Auth 备用按钮组件
 * 当 Google One Tap 不可用时显示
 */
export function NeonFallbackButton() {
  const handleNeonAuth = () => {
    // 这里可以集成 Neon Auth 的登录逻辑
    // 或者简单地跳转到 Neon Auth 页面
    console.log("Neon Auth fallback clicked");
    // 示例：跳转到 Neon Auth 登录页
    // window.location.href = "/neon-auth/sign-in";
  };

  return (
    <div className="mt-4">
      <div className="text-center text-sm text-gray-500 mb-3">或者使用邮箱登录</div>
      <button
        onClick={handleNeonAuth}
        className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        使用邮箱登录
      </button>
    </div>
  );
}
