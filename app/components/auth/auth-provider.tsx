/**
 * Authentication Provider Component
 * Manages global authentication state and provides auth context
 */

import { useEffect } from "react";
import { useAuthStore } from "~/stores/auth-store";
import type { User } from "~/types/common";

interface AuthProviderProps {
  children: React.ReactNode;
  initialUser?: User | null;
}

export function AuthProvider({ children, initialUser }: AuthProviderProps) {
  const { setUser, checkAuth, isAuthenticated } = useAuthStore();

  // Initialize auth state
  useEffect(() => {
    if (initialUser) {
      // Set initial user from server-side data
      setUser(initialUser);
    } else if (!isAuthenticated) {
      // Check auth status if not already authenticated
      checkAuth();
    }
  }, [initialUser, setUser, checkAuth, isAuthenticated]);

  return <>{children}</>;
}

/**
 * Hook to use authentication in components
 */
export function useAuth() {
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);

  const login = useAuthStore((state) => state.login);
  const logout = useAuthStore((state) => state.logout);
  const updateUser = useAuthStore((state) => state.updateUser);
  const updateCredits = useAuthStore((state) => state.updateCredits);
  const clearError = useAuthStore((state) => state.clearError);
  const checkAuth = useAuthStore((state) => state.checkAuth);
  const refreshAuth = useAuthStore((state) => state.refreshAuth);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    updateUser,
    updateCredits,
    clearError,
    checkAuth,
    refreshAuth,
  };
}
