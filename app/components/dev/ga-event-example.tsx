import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
/**
 * Enhanced GA Event Example with multiple tracking scenarios
 */
import { event, trackClick, trackError, trackFormSubmit } from "~/lib/monitoring/analytics";
import { useNotify } from "~/lib/ui/toast";

export function GAEventExampleButton() {
  const { info, error: showError } = useNotify();

  const handleBasicEvent = () => {
    event({
      action: "button_click",
      category: "User Interaction",
      label: "Basic Event Button",
      value: 1,
    });
    info("Basic GA Event Tracked", "Check your GA console (if configured).");
  };

  const handleClickEvent = () => {
    trackClick("Enhanced Click Button", window.location.pathname);
    info("Click Event Tracked", "Enhanced click tracking with location.");
  };

  const handleFormEvent = () => {
    trackFormSubmit("Example Form", true);
    info("Form Submit Tracked", "Form submission event tracked.");
  };

  const handleErrorEvent = () => {
    trackError("User Generated", "Example error for testing", window.location.pathname);
    showError("Error Event Tracked", "Error tracking event sent to GA.");
  };

  const handlePerformanceEvent = () => {
    // Simulate a performance event
    const startTime = performance.now();
    setTimeout(() => {
      const duration = performance.now() - startTime;
      event({
        action: "performance_test",
        category: "Performance",
        label: "Simulated Task",
        value: Math.round(duration),
        custom_parameters: {
          task_type: "simulation",
          duration_ms: duration,
        },
      });
      info("Performance Event Tracked", `Task took ${Math.round(duration)}ms`);
    }, 100);
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Analytics Event Examples</CardTitle>
        <CardDescription>Test different types of Google Analytics events</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <Button onClick={handleBasicEvent} className="w-full" variant="default">
          Track Basic Event
        </Button>

        <Button onClick={handleClickEvent} className="w-full" variant="secondary">
          Track Enhanced Click
        </Button>

        <Button onClick={handleFormEvent} className="w-full" variant="outline">
          Track Form Submit
        </Button>

        <Button onClick={handleErrorEvent} className="w-full" variant="destructive">
          Track Error Event
        </Button>

        <Button onClick={handlePerformanceEvent} className="w-full" variant="ghost">
          Track Performance
        </Button>
      </CardContent>
    </Card>
  );
}
