/**
 * Order model and database operations
 * Updated to use new database operations and queries
 */

import { eq } from "drizzle-orm";
import { dbOperations, dbQueries } from "~/lib/db";
import type { Database } from "~/lib/db/db";
import { orders } from "~/lib/db/schema";
import type { Order as DbOrder } from "~/lib/db/schema";

// Legacy interface for backward compatibility
export interface Order {
  id?: string;
  order_no: string;
  user_uuid: string;
  user_email: string;
  amount: number;
  currency: string;
  interval: "month" | "year" | "one-time";
  product_id: string;
  product_name: string;
  credits?: number;
  valid_months: number;
  status: "created" | "paid" | "cancelled" | "refunded";
  stripe_session_id?: string;
  order_detail?: string;
  expired_at: string;
  created_at: string;
  updated_at?: string;
}

// Convert database order to legacy format
function convertDbOrderToLegacy(dbOrder: DbOrder): Order {
  return {
    id: dbOrder.id,
    order_no: dbOrder.orderNo,
    user_uuid: dbOrder.userUuid,
    user_email: dbOrder.userEmail,
    amount: parseFloat(dbOrder.totalAmount),
    currency: dbOrder.currency,
    interval: "one-time", // Default since new schema doesn't have interval
    product_id: "legacy", // Default since new schema structure is different
    product_name: "Legacy Product", // Default
    credits: 0, // Default
    valid_months: 0, // Default
    status:
      dbOrder.status === "succeeded"
        ? "paid"
        : dbOrder.status === "failed"
          ? "cancelled"
          : "created",
    stripe_session_id: dbOrder.providerOrderId || undefined,
    order_detail:
      typeof dbOrder.orderDetail === "string"
        ? dbOrder.orderDetail
        : dbOrder.orderDetail
          ? JSON.stringify(dbOrder.orderDetail)
          : undefined,
    expired_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // Default 30 days
    created_at: dbOrder.createdAt.toISOString(),
    updated_at: dbOrder.updatedAt.toISOString(),
  };
}

/**
 * Insert new order
 * Note: This function is deprecated. Use the new order operations instead.
 */
export async function insertOrder(order: Order, db: Database): Promise<Order> {
  try {
    // For now, create a minimal order using the new schema
    // This is a temporary solution for backward compatibility
    const now = new Date();

    // Map legacy status to new status
    const statusMap: Record<string, "pending" | "succeeded" | "failed"> = {
      created: "pending",
      paid: "succeeded",
      cancelled: "failed",
      refunded: "failed",
    };

    const result = await db
      .insert(orders)
      .values({
        orderNo: order.order_no,
        accountId: "default-account", // TODO: Get from user context
        billingCustomerId: 1, // TODO: Get from billing customer
        userUuid: order.user_uuid,
        userEmail: order.user_email,
        totalAmount: order.amount.toString(),
        currency: order.currency,
        status: statusMap[order.status] || "pending",
        billingProvider: "stripe",
        providerOrderId: order.stripe_session_id || null,
        orderDetail: order.order_detail ? JSON.parse(order.order_detail) : null,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    const newOrder = result[0];
    return convertDbOrderToLegacy(newOrder);
  } catch (error) {
    console.error("Error inserting order:", error);
    throw error;
  }
}

/**
 * Update order with Stripe session ID
 */
export async function updateOrderSession(
  orderNo: string,
  stripeSessionId: string,
  orderDetail: string,
  db: Database
): Promise<boolean> {
  try {
    await db
      .update(orders)
      .set({
        providerOrderId: stripeSessionId,
        orderDetail: JSON.parse(orderDetail),
        updatedAt: new Date(),
      })
      .where(eq(orders.orderNo, orderNo));

    return true;
  } catch (error) {
    console.error("Error updating order session:", error);
    return false;
  }
}

/**
 * Find order by order number
 * Updated to use new database operations
 */
export async function findOrderByNo(orderNo: string, db: Database): Promise<Order | null> {
  try {
    const result = await db.select().from(orders).where(eq(orders.orderNo, orderNo)).limit(1);

    if (result.length === 0) {
      return null;
    }

    return convertDbOrderToLegacy(result[0]);
  } catch (error) {
    console.error("Error finding order by number:", error);
    return null;
  }
}

/**
 * Find order by Stripe session ID
 * Updated to use new database operations
 */
export async function findOrderBySessionId(sessionId: string, db: Database): Promise<Order | null> {
  try {
    const result = await db
      .select()
      .from(orders)
      .where(eq(orders.providerOrderId, sessionId))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    return convertDbOrderToLegacy(result[0]);
  } catch (error) {
    console.error("Error finding order by session ID:", error);
    return null;
  }
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  orderNo: string,
  status: Order["status"],
  db: Database
): Promise<boolean> {
  try {
    // Map legacy status to new status
    const statusMap: Record<string, "pending" | "succeeded" | "failed"> = {
      created: "pending",
      paid: "succeeded",
      cancelled: "failed",
      refunded: "failed",
    };

    await db
      .update(orders)
      .set({
        status: statusMap[status] || "pending",
        updatedAt: new Date(),
      })
      .where(eq(orders.orderNo, orderNo));

    return true;
  } catch (error) {
    console.error("Error updating order status:", error);
    return false;
  }
}

// New enhanced functions using the new database operations

/**
 * Get user orders with pagination and filtering
 */
export async function getUserOrders(
  userUuid: string,
  db: Database,
  options: {
    page?: number;
    limit?: number;
    sortOrder?: "asc" | "desc";
    status?: string[];
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
) {
  try {
    return await dbOperations.order.getUserOrders(db, userUuid, options);
  } catch (error) {
    console.error("Error getting user orders:", error);
    throw error;
  }
}

/**
 * Search orders with advanced filters
 */
export async function searchOrders(
  db: Database,
  options: {
    search?: string;
    page?: number;
    limit?: number;
    userUuid?: string;
    status?: string[];
    minAmount?: number;
    maxAmount?: number;
    dateFrom?: Date;
    dateTo?: Date;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  } = {}
) {
  try {
    return await dbQueries.order.searchOrders(db, options);
  } catch (error) {
    console.error("Error searching orders:", error);
    throw error;
  }
}

/**
 * Get order analytics
 */
export async function getOrderAnalytics(
  db: Database,
  options: {
    dateFrom?: Date;
    dateTo?: Date;
    accountId?: string;
  } = {}
) {
  try {
    return await dbQueries.order.getOrderAnalytics(db, options);
  } catch (error) {
    console.error("Error getting order analytics:", error);
    throw error;
  }
}

/**
 * Get order with items
 */
export async function getOrderWithItems(orderId: string, db: Database) {
  try {
    return await dbOperations.order.getWithItems(db, orderId);
  } catch (error) {
    console.error("Error getting order with items:", error);
    return null;
  }
}
