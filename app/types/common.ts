// Common type definitions for the application

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: unknown;
}

export interface Order {
  id: string;
  userId: string;
  status: string;
  total: number;
  currency?: string;
  items?: OrderItem[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: unknown;
}

export interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  name?: string;
  [key: string]: unknown;
}

export interface FilterValue {
  [key: string]: string | number | boolean | string[] | undefined;
}

export interface GoogleNotification {
  credential?: string;
  select_by?: string;
  [key: string]: unknown;
}

export interface GoogleRenderOptions {
  theme?: "outline" | "filled_blue" | "filled_black";
  size?: "large" | "medium" | "small";
  type?: "standard" | "icon";
  shape?: "rectangular" | "pill" | "circle" | "square";
  text?: "signin_with" | "signup_with" | "continue_with" | "signin";
  logo_alignment?: "left" | "center";
  width?: string;
  locale?: string;
  [key: string]: unknown;
}
