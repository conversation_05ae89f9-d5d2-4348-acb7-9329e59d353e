/**
 * Credit system service
 */

import { eq, sql, sum } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { creditTransactions, users } from "~/lib/db/schema";

// Generate unique transaction number
function generateTransactionNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `TXN-${timestamp}-${random}`.toUpperCase();
}
import { updateUserCredits } from "~/models/user";

export enum CreditsTransType {
  Ping = "ping",
  Purchase = "purchase",
  Reward = "reward",
  Refund = "refund",
  AITextGeneration = "ai_text_generation",
  AIImageGeneration = "ai_image_generation",
  AIStreamText = "ai_stream_text",
  AIEmbedding = "ai_embedding",
  AITextClassification = "ai_text_classification",
  AIImageClassification = "ai_image_classification",
  AISpeechToText = "ai_speech_to_text",
}

export enum CreditsAmount {
  PingCost = 1,
  DefaultReward = 10,
  AITextGenerationCost = 5,
  AIImageGenerationCost = 10,
  AIStreamTextCost = 3,
  AIEmbeddingCost = 2,
  AITextClassificationCost = 1,
  AIImageClassificationCost = 3,
  AISpeechToTextCost = 4,
}

export interface CreditTransaction {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
  description?: string;
}

/**
 * Get user's current credits
 */
export async function getUserCredits(userUuid: string, db: Database): Promise<number> {
  try {
    const result = await db
      .select({ credits: users.credits })
      .from(users)
      .where(eq(users.uuid, userUuid))
      .limit(1);

    if (result.length === 0) {
      return 0;
    }

    return result[0].credits || 0;
  } catch (error) {
    console.error("Error getting user credits:", error);
    return 0;
  }
}

/**
 * Decrease user credits
 */
export async function decreaseCredits(
  transaction: CreditTransaction,
  db: Database
): Promise<boolean> {
  try {
    // Start a transaction to ensure atomicity
    const currentCredits = await getUserCredits(transaction.user_uuid, db);

    if (currentCredits < transaction.credits) {
      console.error(
        `Insufficient credits: user has ${currentCredits}, needs ${transaction.credits}`
      );
      return false;
    }

    const newCredits = currentCredits - transaction.credits;

    // Update user credits
    const updateSuccess = await updateUserCredits(transaction.user_uuid, newCredits, db);
    if (!updateSuccess) {
      return false;
    }

    // Log the transaction
    await db.insert(creditTransactions).values({
      transNo: generateTransactionNumber(),
      userUuid: transaction.user_uuid,
      transType: transaction.trans_type,
      credits: -transaction.credits, // Negative for decrease
      description: transaction.description || `Credits used for ${transaction.trans_type}`,
      createdAt: new Date(),
    });

    console.log(
      `Decreased ${transaction.credits} credits for user ${transaction.user_uuid} (${transaction.trans_type})`
    );
    return true;
  } catch (error) {
    console.error("Error decreasing credits:", error);
    return false;
  }
}

/**
 * Increase user credits
 */
export async function increaseCredits(
  transaction: CreditTransaction,
  db: Database
): Promise<boolean> {
  try {
    const currentCredits = await getUserCredits(transaction.user_uuid, db);
    const newCredits = currentCredits + transaction.credits;

    // Update user credits
    const updateSuccess = await updateUserCredits(transaction.user_uuid, newCredits, db);
    if (!updateSuccess) {
      return false;
    }

    // Log the transaction
    await db.insert(creditTransactions).values({
      transNo: generateTransactionNumber(),
      userUuid: transaction.user_uuid,
      transType: transaction.trans_type,
      credits: transaction.credits, // Positive for increase
      description: transaction.description || `Credits added for ${transaction.trans_type}`,
      createdAt: new Date(),
    });

    console.log(
      `Increased ${transaction.credits} credits for user ${transaction.user_uuid} (${transaction.trans_type})`
    );
    return true;
  } catch (error) {
    console.error("Error increasing credits:", error);
    return false;
  }
}

/**
 * Check if user has enough credits
 */
export async function hasEnoughCredits(
  userUuid: string,
  requiredCredits: number,
  db: Database
): Promise<boolean> {
  const currentCredits = await getUserCredits(userUuid, db);
  return currentCredits >= requiredCredits;
}

/**
 * Get credit transaction history for a user
 */
export async function getCreditTransactionHistory(
  userUuid: string,
  db: Database,
  limit = 50
): Promise<any[]> {
  try {
    const result = await db
      .select()
      .from(creditTransactions)
      .where(eq(creditTransactions.userUuid, userUuid))
      .orderBy(sql`${creditTransactions.createdAt} DESC`)
      .limit(limit);

    return result.map((transaction) => ({
      id: transaction.id,
      user_uuid: transaction.userUuid,
      trans_type: transaction.transType,
      credits: transaction.credits,
      description: transaction.description,
      created_at: transaction.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting credit transaction history:", error);
    return [];
  }
}
