/* Unified American-Friendly Design System */
:root {
  /* Light theme colors - Clean, professional, trustworthy */
  --background: 0 0% 100%;
  --foreground: 224 71% 4%;
  --card: 0 0% 100%;
  --card-foreground: 224 71% 4%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71% 4%;

  /* Primary: Professional blue for trust and reliability */
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;

  /* Secondary: Clean grays for subtle elements */
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;

  /* Muted: Light backgrounds and subtle text */
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;

  /* Accent: Slightly warmer for interactive elements */
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;

  /* Destructive: Clear red for errors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  /* Borders and inputs: Subtle but defined */
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221 83% 53%;

  /* Border radius: Modern but not too rounded */
  --radius: 0.75rem;

  /* Success color for positive actions */
  --success: 142 76% 36%;
  --success-foreground: 355 100% 97%;

  /* Warning color for cautions */
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
}

.dark {
  /* Dark theme colors - Professional dark mode */
  --background: 224 71% 4%;
  --foreground: 210 40% 98%;
  --card: 224 71% 4%;
  --card-foreground: 210 40% 98%;
  --popover: 224 71% 4%;
  --popover-foreground: 210 40% 98%;

  /* Primary: Bright blue for dark mode visibility */
  --primary: 217 91% 60%;
  --primary-foreground: 224 71% 4%;

  /* Secondary: Dark grays with good contrast */
  --secondary: 215 27.9% 16.9%;
  --secondary-foreground: 210 40% 98%;

  /* Muted: Darker backgrounds with readable text */
  --muted: 215 27.9% 16.9%;
  --muted-foreground: 217.9 10.6% 64.9%;

  /* Accent: Slightly lighter for interaction */
  --accent: 215 27.9% 16.9%;
  --accent-foreground: 210 40% 98%;

  /* Destructive: Softer red for dark mode */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  /* Borders and inputs: Visible but not harsh */
  --border: 215 27.9% 16.9%;
  --input: 215 27.9% 16.9%;
  --ring: 217 91% 60%;

  /* Success and warning for dark mode */
  --success: 142 76% 36%;
  --success-foreground: 355 100% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
}

/* Ensure proper contrast for text */
.light {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

/* Base styles for American-friendly design */
body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography scale for clear hierarchy */
.text-display {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-headline {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-title {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
}

.text-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

.text-caption {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
}

/* American-style button gradients */
.btn-primary-gradient {
  background: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(262 83% 58%) 100%);
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.btn-primary-gradient:hover {
  background-position: right center;
  box-shadow: 0 10px 25px -5px hsl(221 83% 53% / 0.4);
}

/* Card styles with American preferences */
.card-modern {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px 0 hsl(0 0% 0% / 0.06);
  transition: all 0.2s ease;
}

.card-modern:hover {
  box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -2px hsl(0 0% 0% / 0.05);
  transform: translateY(-2px);
}

/* Note: .glass class definition moved to animations.css to avoid duplication */

/* Focus styles for accessibility */
.focus-ring {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Gradient backgrounds for sections */
.bg-gradient-light {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(210 40% 98%) 100%);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, hsl(221 83% 53% / 0.1) 0%, hsl(262 83% 58% / 0.1) 100%);
}