/**
 * Neon Auth Login Page
 * Alternative login page using Neon Auth
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON><PERSON><PERSON>gle, ArrowLeft } from "lucide-react";
import { NeonAuthProvider, useNeonAuthStatus } from "~/components/auth/neon-auth-provider";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getAuthConfig, validateAuthConfig } from "~/lib/auth/auth-config";
import { getUnifiedUser } from "~/lib/auth/unified-middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign In with Neon Auth - AI SaaS Starter" },
    { name: "description", content: "Sign in to your account using Neon Auth" },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  const authConfig = getAuthConfig(context.cloudflare?.env);

  // If Neon Auth is not enabled, redirect to regular login
  if (!authConfig.neonAuth.enabled) {
    return redirect("/auth/login");
  }

  // Check if user is already authenticated
  const userResult = await getUnifiedUser(request, context.cloudflare?.env);
  if (userResult.success) {
    return redirect("/console");
  }

  // Validate configuration
  const validation = validateAuthConfig(authConfig);

  return json({
    authConfig: {
      projectId: authConfig.neonAuth.projectId,
      publishableKey: authConfig.neonAuth.publishableKey,
      enabled: authConfig.neonAuth.enabled,
    },
    validation,
    googleOneTapAvailable: authConfig.googleOneTap.enabled,
  });
}

function NeonAuthLoginContent() {
  const { authConfig, validation, googleOneTapAvailable } = useLoaderData<typeof loader>();
  const neonAuthStatus = useNeonAuthStatus();

  if (!validation.valid) {
    return (
      <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800 dark:text-red-200">Configuration Error</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <ul className="list-disc list-inside space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
              {googleOneTapAvailable && (
                <div className="mt-4">
                  <Link
                    to="/auth/login"
                    className="inline-flex items-center gap-2 text-sm font-medium text-red-800 dark:text-red-200 hover:text-red-900 dark:hover:text-red-100"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Use Google One Tap Login
                  </Link>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!neonAuthStatus.configured) {
    return (
      <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                Neon Auth Loading
              </h3>
              <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                Neon Auth is initializing. Please wait a moment...
              </p>
              <div className="mt-4 flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                <span className="text-sm text-yellow-700 dark:text-yellow-300">Loading...</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Sign In with Neon Auth</CardTitle>
          <CardDescription>
            Secure authentication powered by Neon's built-in auth system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Neon Auth provides seamless authentication with your data stored directly in your Neon
              database.
            </p>
            <div className="space-y-4">
              <a
                href="/neon-auth/sign-up"
                className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                Create Account
              </a>
              <a
                href="/neon-auth/sign-in"
                className="block w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                Sign In
              </a>
            </div>
          </div>
        </CardContent>
      </Card>

      {googleOneTapAvailable && (
        <Card className="border-gray-200 bg-gray-50 dark:bg-gray-800/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Prefer the original authentication method?
              </p>
              <Link
                to="/auth/login"
                className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <ArrowLeft className="h-4 w-4" />
                Use Google One Tap Login
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default function NeonAuthLoginPage() {
  const { authConfig } = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout
      hero={{
        title: "Welcome Back!",
        description: "Sign in to access your dashboard and services with Neon Auth.",
      }}
    >
      <section className="py-16">
        <div className="max-w-md mx-auto">
          <NeonAuthProvider authConfig={authConfig}>
            <NeonAuthLoginContent />
          </NeonAuthProvider>
        </div>
      </section>
    </UnifiedLayout>
  );
}
