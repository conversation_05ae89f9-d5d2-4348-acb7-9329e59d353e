/**
 * 极简用户信息 API
 * 返回当前用户信息
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { getUser } from "~/lib/auth/middleware.server";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const userResult = await getUser(request);

    if (userResult.success && userResult.user) {
      return json({
        success: true,
        user: {
          id: userResult.user.id,
          uuid: userResult.user.uuid,
          email: userResult.user.email,
          name: userResult.user.name,
          avatar: userResult.user.avatar,
          credits: userResult.user.credits,
        },
      });
    } else {
      return json(
        {
          success: false,
          error: userResult.error || "Not authenticated",
        },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Auth me API error:", error);
    return json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
