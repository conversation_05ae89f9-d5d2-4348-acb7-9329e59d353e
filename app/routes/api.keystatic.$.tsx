// Temporarily disabled due to import issues with @keystatic/remix
// TODO: Fix Keystatic API route handler import
// import { makeAPIRouteHandler } from '@keystatic/remix/api';
// import config from '../../keystatic.config';

// export const { action, loader } = makeAPIRouteHandler({
//   config,
// });

import { json } from "@remix-run/cloudflare";

export const action = () => {
  return json({ error: "Keystatic API temporarily disabled" }, { status: 503 });
};

export const loader = () => {
  return json({ error: "Keystatic API temporarily disabled" }, { status: 503 });
};
