/**
 * R2 Storage Test Page
 * Page for testing R2 storage functionality
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json, useLoaderData } from "@remix-run/react";
import { useEffect, useState } from "react";

export async function loader({ context }: LoaderFunctionArgs) {
  const R2_BUCKET = context.cloudflare.env.R2_BUCKET;

  return json({
    r2Configured: !!R2_BUCKET,
    bucketName: context.cloudflare.env.R2_BUCKET_NAME || "remix-starter-r2",
  });
}

export default function DevR2Test() {
  const { r2Configured, bucketName } = useLoaderData<typeof loader>();
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [fileList, setFileList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Upload file
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/r2-upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setUploadResult(result.file);
        // Refresh file list
        await loadFileList();
      } else {
        setError(result.error || "Upload failed");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Upload failed");
    } finally {
      setLoading(false);
    }
  };

  // Load file list
  const loadFileList = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/r2-list");
      const result = await response.json();

      if (result.success) {
        setFileList(result.files);
      } else {
        setError(result.error || "Failed to load file list");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load file list");
    } finally {
      setLoading(false);
    }
  };

  // Delete file
  const handleDeleteFile = async (key: string) => {
    if (!confirm(`Are you sure you want to delete file ${key}?`)) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/r2-list?key=${encodeURIComponent(key)}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        // Refresh file list
        await loadFileList();
      } else {
        setError(result.error || "Delete failed");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Delete failed");
    } finally {
      setLoading(false);
    }
  };

  // Get file list when page loads
  useEffect(() => {
    if (r2Configured) {
      loadFileList();
    }
  }, [r2Configured]);

  if (!r2Configured) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h1 className="text-xl font-bold text-yellow-800 mb-2">R2 Storage Not Configured</h1>
          <p className="text-yellow-700">
            R2 bucket binding is not configured. Please check your wrangler.toml configuration.
          </p>
          <div className="mt-4 text-sm text-yellow-600">
            <p>Expected configuration in wrangler.toml:</p>
            <pre className="bg-yellow-100 p-2 rounded mt-2">
              {`[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "${bucketName}"`}
            </pre>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">R2 Storage Test</h1>

      {/* Status information */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold text-green-800 mb-2">✅ R2 Storage Configured</h2>
        <p className="text-green-700">Bucket: {bucketName}</p>
      </div>

      {/* Error information */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">❌ Error</h3>
          <p className="text-red-700">{error}</p>
          <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 underline">
            Dismiss
          </button>
        </div>
      )}

      {/* File upload */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">📤 Upload File</h2>
        <div className="space-y-4">
          <input
            type="file"
            onChange={handleFileUpload}
            disabled={loading}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />

          {loading && <div className="text-blue-600">⏳ Processing...</div>}

          {uploadResult && (
            <div className="bg-green-50 border border-green-200 rounded p-4">
              <h3 className="font-semibold text-green-800 mb-2">✅ Upload Successful</h3>
              <div className="text-sm text-green-700 space-y-1">
                <p>
                  <strong>Key:</strong> {uploadResult.key}
                </p>
                <p>
                  <strong>Size:</strong> {uploadResult.size} bytes
                </p>
                <p>
                  <strong>Type:</strong> {uploadResult.contentType}
                </p>
                <p>
                  <strong>ETag:</strong> {uploadResult.etag}
                </p>
                <a
                  href={uploadResult.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block mt-2 text-blue-600 underline"
                >
                  📥 Download File
                </a>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* File list */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">📋 File List</h2>
          <button
            onClick={loadFileList}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            🔄 Refresh
          </button>
        </div>

        {fileList.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No files found</p>
        ) : (
          <div className="space-y-2">
            {fileList.map((file) => (
              <div
                key={file.key}
                className="flex items-center justify-between p-3 bg-gray-50 rounded"
              >
                <div className="flex-1">
                  <div className="font-medium">{file.filename}</div>
                  <div className="text-sm text-gray-500">
                    {file.formattedSize} • {file.contentType || "Unknown type"} •{" "}
                    {new Date(file.lastModified).toLocaleString(undefined, {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </div>
                  <div className="text-xs text-gray-400">{file.key}</div>
                </div>
                <div className="flex space-x-2">
                  <a
                    href={file.downloadUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600"
                  >
                    📥 Download
                  </a>
                  {file.isImage && (
                    <a
                      href={file.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                    >
                      👁️ Preview
                    </a>
                  )}
                  <button
                    onClick={() => handleDeleteFile(file.key)}
                    disabled={loading}
                    className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 disabled:opacity-50"
                  >
                    🗑️ Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
