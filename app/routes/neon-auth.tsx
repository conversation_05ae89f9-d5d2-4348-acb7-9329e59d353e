/**
 * Neon Auth Handler Routes
 * Handles all Neon Auth authentication flows
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Outlet, useLoaderData } from "@remix-run/react";
import { Neon<PERSON><PERSON>Provider, NeonAuthHandler } from "~/components/auth/neon-auth-provider";
import { getAuthConfig, validateAuthConfig } from "~/lib/auth/auth-config";

export const meta: MetaFunction = () => {
  return [
    { title: "Authentication - AI SaaS Starter" },
    { name: "description", content: "Secure authentication powered by Neon Auth" },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  const authConfig = getAuthConfig(context.cloudflare?.env);

  // If Neon Auth is not enabled, redirect to regular login
  if (!authConfig.neonAuth.enabled) {
    return redirect("/auth/login");
  }

  // Validate configuration
  const validation = validateAuthConfig(authConfig);
  if (!validation.valid) {
    console.error("Neon Auth configuration errors:", validation.errors);
    return redirect("/auth/login");
  }

  // Return safe client configuration
  return json({
    authConfig: {
      projectId: authConfig.neonAuth.projectId,
      publishableKey: authConfig.neonAuth.publishableKey,
      enabled: authConfig.neonAuth.enabled,
    },
    errors: validation.errors,
  });
}

export default function NeonAuthLayout() {
  const { authConfig, errors } = useLoaderData<typeof loader>();

  // Show configuration errors if any
  if (errors.length > 0) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Configuration Error</h3>
            <div className="mt-2 text-sm text-gray-500">
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
            <div className="mt-4">
              <a
                href="/auth/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Use Alternative Login
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <NeonAuthProvider authConfig={authConfig}>
      <div className="min-h-screen bg-gray-50">
        <Outlet />
      </div>
    </NeonAuthProvider>
  );
}

// Child route for handling authentication flows
export function NeonAuthHandlerRoute() {
  return <NeonAuthHandler fullPage={true} />;
}
