/**
 * User Registration Page - Optional Traditional Registration
 * Provides a dedicated signup page with invite code support
 */

import type { LoaderFunctionArgs, MetaFunction, ActionFunctionArgs } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Link, useLoaderData, useActionData, Form } from "@remix-run/react";
import { useState } from "react";
import { GoogleOneTap, NeonFallbackButton } from "~/components/auth/google-one-tap";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { getUser } from "~/lib/auth/middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign Up - AI SaaS Starter" },
    { name: "description", content: "Create your account and start using AI tools" },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Check if user is already authenticated
  const userResult = await getUser(request);

  if (userResult.success) {
    // User is already authenticated, redirect to console
    return redirect("/console");
  }

  // Get invite code from URL if present
  const url = new URL(request.url);
  const inviteCode = url.searchParams.get("invite");

  return json({
    googleClientId: context.cloudflare?.env?.GOOGLE_CLIENT_ID,
    inviteCode,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  // Handle traditional email registration (if implementing custom registration)
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const inviteCode = formData.get("inviteCode") as string;

  // For now, redirect to login with a message
  // TODO: Implement custom email registration if needed
  return json({
    success: false,
    message: "Please use Google Sign-In or Neon Auth for registration",
  });
}

export default function RegisterPage() {
  const { googleClientId, inviteCode } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [showInviteCode, setShowInviteCode] = useState(!!inviteCode);

  return (
    <UnifiedLayout showHeader={false} showFooter={false} showSidebar={false} containerSize="full">
      {/* Background Effects */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-1/4 w-24 h-24 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-pulse delay-2000" />
      </div>

      <section className="py-32 relative overflow-hidden min-h-screen flex items-center">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-md mx-auto">

            {/* Registration Card */}
            <div className="group relative animate-fade-in-up delay-200">
              <div className="absolute -inset-1 bg-gradient-to-r from-green-600/20 via-blue-600/20 to-purple-600/20 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-1000" />
              <Card className="relative shadow-2xl border-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-3xl overflow-hidden">
                <CardHeader className="text-center pb-6 pt-8">
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                    Choose Your Registration Method
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    Quick signup with Google or email registration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 pb-8">
                  {/* Google One Tap Registration */}
                  <GoogleOneTap clientId={googleClientId} enabled={!!googleClientId} />

                  {/* Neon Auth Registration */}
                  <NeonFallbackButton />

                  {/* Invite Code Section */}
                  {(showInviteCode || inviteCode) && (
                    <div className="space-y-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                      <div className="flex items-center gap-2">
                        <svg
                          className="w-5 h-5 text-green-500"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v13m0-13V6a2 2 0 112 2h-1m-1 0V6a2 2 0 00-2 2v2m0 0V6a2 2 0 00-2 2v2m0 0h6m-6 0H9m3 0h3"
                          />
                        </svg>
                        <span className="text-sm font-medium text-green-700 dark:text-green-300">
                          {inviteCode ? "Invite Code Applied!" : "Have an invite code?"}
                        </span>
                      </div>
                      {inviteCode ? (
                        <p className="text-sm text-green-600 dark:text-green-400">
                          Code:{" "}
                          <code className="bg-green-100 dark:bg-green-800 px-2 py-1 rounded">
                            {inviteCode}
                          </code>
                          <br />
                          You'll get 50 bonus credits when you sign up!
                        </p>
                      ) : (
                        <Form method="post" className="space-y-3">
                          <div>
                            <Label
                              htmlFor="inviteCode"
                              className="text-sm text-green-700 dark:text-green-300"
                            >
                              Invite Code (Optional)
                            </Label>
                            <Input
                              id="inviteCode"
                              name="inviteCode"
                              type="text"
                              placeholder="Enter invite code for bonus credits"
                              className="mt-1"
                            />
                          </div>
                          <Button type="submit" size="sm" variant="outline" className="w-full">
                            Apply Invite Code
                          </Button>
                        </Form>
                      )}
                    </div>
                  )}

                  {!showInviteCode && !inviteCode && (
                    <button
                      onClick={() => setShowInviteCode(true)}
                      className="w-full text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Have an invite code? Click here
                    </button>
                  )}

                  {/* Action Data Message */}
                  {actionData?.message && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">{actionData.message}</p>
                    </div>
                  )}

                  {/* Terms */}
                  <div className="text-center text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-700">
                    By creating an account, you agree to our{" "}
                    <Link
                      to="/legal/terms"
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link
                      to="/legal/privacy"
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Privacy Policy
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sign In Link */}
            <div className="text-center mt-8 animate-fade-in-up delay-400">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Already have an account?{" "}
                <Link
                  to="/auth/login"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                >
                  Sign in here
                </Link>
              </p>
            </div>

            {/* Benefits */}
            <div className="text-center mt-6 animate-fade-in-up delay-500">
              <div className="flex items-center justify-center gap-6 text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <svg
                    className="w-4 h-4 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  100 Free Credits
                </div>
                <div className="flex items-center gap-1">
                  <svg
                    className="w-4 h-4 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  No Credit Card
                </div>
                <div className="flex items-center gap-1">
                  <svg
                    className="w-4 h-4 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  Instant Access
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
