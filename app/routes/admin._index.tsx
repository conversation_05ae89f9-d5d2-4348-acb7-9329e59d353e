import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import { Link } from "@remix-run/react";

// Type definitions
interface User {
  uuid: string;
  name: string;
  email: string;
  avatar?: string;
  plan: string;
  credits: number;
}

interface SystemHealth {
  database: string;
  api: string;
  storage: string;
  uptime: string;
  responseTime: string;
  errorRate: string;
}
import {
  Activity,
  AlertCircle,
  Bot,
  CheckCircle,
  DollarSign,
  Eye,
  FileText,
  MessageSquare,
  RefreshCw,
  ShoppingCart,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import AdminLayout from "~/components/layout/admin-layout";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";

export const meta: MetaFunction = () => {
  return [
    { title: "Admin Dashboard - AI SaaS Starter" },
    {
      name: "description",
      content: "Admin dashboard for managing users, orders, content, and analytics.",
    },
  ];
};

export async function loader({ context }: LoaderFunctionArgs) {
  // Initial data load - we'll also fetch real-time data on the client
  return json({
    timestamp: new Date().toISOString(),
    initialLoad: true,
  });
}

// Default stats structure - will be populated with real data
const defaultStats = [
  {
    title: "Total Users",
    value: "Loading...",
    change: "...",
    trend: "up" as const,
    icon: <Users className="h-4 w-4" />,
  },
  {
    title: "Monthly Revenue",
    value: "Loading...",
    change: "...",
    trend: "up" as const,
    icon: <DollarSign className="h-4 w-4" />,
  },
  {
    title: "Active Orders",
    value: "Loading...",
    change: "...",
    trend: "up" as const,
    icon: <ShoppingCart className="h-4 w-4" />,
  },
  {
    title: "AI Requests",
    value: "Loading...",
    change: "...",
    trend: "up" as const,
    icon: <Bot className="h-4 w-4" />,
  },
];

const recentActivity = [
  {
    id: 1,
    type: "user",
    message: "New user registration: <EMAIL>",
    time: "2 minutes ago",
    icon: <Users className="h-4 w-4" />,
  },
  {
    id: 2,
    type: "order",
    message: "New Pro subscription order #1234",
    time: "5 minutes ago",
    icon: <ShoppingCart className="h-4 w-4" />,
  },
  {
    id: 3,
    type: "ai",
    message: "High AI usage detected <NAME_EMAIL>",
    time: "10 minutes ago",
    icon: <Bot className="h-4 w-4" />,
  },
  {
    id: 4,
    type: "content",
    message: "New blog post published: 'AI Best Practices'",
    time: "1 hour ago",
    icon: <FileText className="h-4 w-4" />,
  },
  {
    id: 5,
    type: "feedback",
    message: "New feedback received with 5-star rating",
    time: "2 hours ago",
    icon: <MessageSquare className="h-4 w-4" />,
  },
];

const quickActions = [
  {
    title: "View Users",
    description: "Manage user accounts and permissions",
    href: "/admin/users",
    icon: <Users className="h-5 w-5" />,
    color: "bg-blue-500",
  },
  {
    title: "Process Orders",
    description: "Review and manage customer orders",
    href: "/admin/orders",
    icon: <ShoppingCart className="h-5 w-5" />,
    color: "bg-green-500",
  },
  {
    title: "AI Analytics",
    description: "Monitor AI tool usage and performance",
    href: "/admin/ai-tools",
    icon: <Bot className="h-5 w-5" />,
    color: "bg-purple-500",
  },
  {
    title: "AI Test Suite",
    description: "Test and monitor AI provider performance",
    href: "/ai-test-suite",
    icon: <Bot className="h-5 w-5" />,
    color: "bg-indigo-500",
  },
  {
    title: "Content Management",
    description: "Create and edit blog posts and pages",
    href: "/admin/content",
    icon: <FileText className="h-5 w-5" />,
    color: "bg-orange-500",
  },
];

export default function AdminDashboard() {
  const loaderData = useLoaderData<typeof loader>();
  const [stats, setStats] = useState(defaultStats);
  const [recentUsers, setRecentUsers] = useState<User[]>([]);
  const [recentOrders, setRecentOrders] = useState<any[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [timeRange, setTimeRange] = useState("30d");
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string>("");

  // Fetch dashboard data
  const fetchDashboardData = async (range: string = timeRange) => {
    setIsLoading(true);
    try {
      // Fetch overview stats
      const statsResponse = await fetch("/api/admin/dashboard", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "get-overview-stats", timeRange: range }),
      });
      const statsData = await statsResponse.json();

      if (statsData.code === 0) {
        const newStats = [
          {
            title: "Total Users",
            value: statsData.data.stats.totalUsers,
            change: statsData.data.stats.userGrowth,
            trend: statsData.data.stats.userGrowth.startsWith("+")
              ? ("up" as const)
              : ("down" as const),
            icon: <Users className="h-4 w-4" />,
          },
          {
            title: "Monthly Revenue",
            value: statsData.data.stats.totalRevenue,
            change: statsData.data.stats.revenueGrowth,
            trend: statsData.data.stats.revenueGrowth.startsWith("+")
              ? ("up" as const)
              : ("down" as const),
            icon: <DollarSign className="h-4 w-4" />,
          },
          {
            title: "Active Orders",
            value: statsData.data.stats.activeOrders,
            change: statsData.data.stats.orderGrowth,
            trend: statsData.data.stats.orderGrowth.startsWith("+")
              ? ("up" as const)
              : ("down" as const),
            icon: <ShoppingCart className="h-4 w-4" />,
          },
          {
            title: "AI Requests",
            value: statsData.data.stats.aiRequests,
            change: statsData.data.stats.aiGrowth,
            trend: statsData.data.stats.aiGrowth.startsWith("+")
              ? ("up" as const)
              : ("down" as const),
            icon: <Bot className="h-4 w-4" />,
          },
        ];
        setStats(newStats);
      }

      // Fetch recent users
      const usersResponse = await fetch("/api/admin/dashboard", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "get-recent-users" }),
      });
      const usersData = await usersResponse.json();
      if (usersData.code === 0) {
        setRecentUsers(usersData.data.users);
      }

      // Fetch system health
      const healthResponse = await fetch("/api/admin/dashboard", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "get-system-health" }),
      });
      const healthData = await healthResponse.json();
      if (healthData.code === 0) {
        setSystemHealth(healthData.data.health);
      }

      setLastUpdated(
        new Date().toLocaleTimeString(undefined, {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        })
      );
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on mount and when time range changes
  useEffect(() => {
    fetchDashboardData(timeRange);
  }, [timeRange]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(
      () => {
        fetchDashboardData();
      },
      5 * 60 * 1000
    );

    return () => clearInterval(interval);
  }, [timeRange]);

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's what's happening with your SaaS platform.
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchDashboardData()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
          </div>
        </div>

        {lastUpdated && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>Last updated: {lastUpdated}</AlertDescription>
          </Alert>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {stat.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                  )}
                  <span className={stat.trend === "up" ? "text-green-500" : "text-red-500"}>
                    {stat.change}
                  </span>
                  <span className="ml-1">from last month</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common administrative tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <Link
                    key={action.title}
                    to={action.href}
                    className="flex items-start gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div className={`p-2 rounded-md ${action.color} text-white`}>{action.icon}</div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm">{action.title}</p>
                      <p className="text-xs text-muted-foreground">{action.description}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest system events and user actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className="p-2 bg-muted rounded-md">{activity.icon}</div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm">{activity.message}</p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <Button variant="outline" className="w-full" asChild>
                  <Link to="/admin/activity">
                    <Activity className="h-4 w-4 mr-2" />
                    View All Activity
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Users */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Users</CardTitle>
            <CardDescription>Latest user registrations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentUsers.length > 0 ? (
                recentUsers.map((user, index) => (
                  <div key={user.uuid} className="flex items-center gap-4">
                    <div className="flex items-center gap-3 flex-1">
                      <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center text-sm font-medium">
                        #{index + 1}
                      </div>
                      {user.avatar ? (
                        <img src={user.avatar} alt={user.name} className="w-10 h-10 rounded-full" />
                      ) : (
                        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                          <span className="text-primary-foreground font-medium">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{user.name}</p>
                        <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={user.plan === "Pro" ? "default" : "secondary"}>
                        {user.plan}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">{user.credits} credits</p>
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link to={`/admin/users/${user.uuid}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {isLoading ? "Loading users..." : "No recent users found"}
                </div>
              )}
            </div>
            <div className="mt-4 pt-4 border-t">
              <Button variant="outline" className="w-full" asChild>
                <Link to="/admin/users">View All Users</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Health */}
        {systemHealth && (
          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
              <CardDescription>Current system status and performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Database</span>
                    <Badge
                      variant={systemHealth.database === "healthy" ? "default" : "destructive"}
                    >
                      {systemHealth.database}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">API</span>
                    <Badge variant={systemHealth.api === "healthy" ? "default" : "destructive"}>
                      {systemHealth.api}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Storage</span>
                    <Badge variant={systemHealth.storage === "healthy" ? "default" : "destructive"}>
                      {systemHealth.storage}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Uptime</span>
                    <span className="text-sm font-medium">{systemHealth.uptime}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Response Time</span>
                    <span className="text-sm font-medium">{systemHealth.responseTime}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Error Rate</span>
                    <span className="text-sm font-medium">{systemHealth.errorRate}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
