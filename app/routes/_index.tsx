import type { MetaFunction } from "@remix-run/cloudflare";
import { useLoaderData, useSearchParams } from "@remix-run/react";
import { useEffect, useState } from "react";
import AIChatInterface from "~/components/ai-chat-interface";
import Showcase from "~/components/blocks/showcase";
import Sidebar, { type ChatConversation } from "~/components/layout/sidebar";
import UnifiedLayout from "~/components/layout/unified-layout";
import { getLandingPageConfig } from "~/config/landing";
import { siteConfig } from "~/config/seo";
import { generateStructuredData } from "~/lib/seo/seo";

export const meta: MetaFunction = () => {
  const title = "AI SaaS Starter - Build Amazing AI Applications";
  const description =
    "A modern SaaS starter template with Remix, Cloudflare, Neon Database, and powerful AI tools integration. Get started in minutes, not months.";

  return [
    { title },
    { name: "description", content: description },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:type", content: "website" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
    {
      "script:ld+json": generateStructuredData({
        type: "WebSite",
        name: title,
        description,
        url: siteConfig.url,
      }),
    },
  ];
};

export async function loader() {
  const config = getLandingPageConfig();

  return {
    config,
  };
}

function ChatSidebarWrapper() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const currentConversationId = searchParams.get("conversation");

  // Load conversations
  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/chat/conversations");
      if (response.ok) {
        const data = (await response.json()) as any;
        if (data.success) {
          setConversations(data.data.conversations || []);
        } else {
          // Handle API error but don't crash the UI
          console.warn("API returned error:", data.error);
          setConversations([]);
        }
      } else {
        console.warn("Failed to fetch conversations:", response.status, response.statusText);
        setConversations([]);
      }
    } catch (error) {
      console.error("Failed to load conversations:", error);
      // Set empty conversations instead of crashing
      setConversations([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = () => {
    setSearchParams({});
  };

  const handleSelectConversation = (conversationId: string) => {
    setSearchParams({ conversation: conversationId });
  };

  const handleArchiveConversation = async (conversationId: string) => {
    try {
      const response = await fetch("/api/chat/conversations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "archive",
          conversationId,
        }),
      });

      if (response.ok) {
        // Remove from local state
        setConversations((prev) => prev.filter((conv) => conv.id !== conversationId));

        // If this was the current conversation, clear it
        if (currentConversationId === conversationId) {
          setSearchParams({});
        }
      }
    } catch (error) {
      console.error("Failed to archive conversation:", error);
    }
  };

  return (
    <Sidebar
      mode="chat"
      title="AI Chat"
      showCloseButton={false}
      conversations={conversations}
      currentConversationId={currentConversationId || undefined}
      onNewChat={handleNewChat}
      onSelectConversation={handleSelectConversation}
      onArchiveConversation={handleArchiveConversation}
      isLoadingConversations={isLoading}
    />
  );
}

export default function Index() {
  const { config } = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout
      showSidebar={true}
      showHeader={false}
      showFooter={false}
      customSidebar={<ChatSidebarWrapper />}
      className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-background dark:to-purple-950/20"
    >
      {/* Background decorative elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-1/4 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse delay-2000" />
      </div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Chat Interface */}
        <div className="flex-1 max-w-4xl mx-auto w-full px-4">
          <AIChatInterface className="h-full" />
        </div>

        {/* Showcase Section */}
        <div className="mt-8">
          <Showcase title={config.showcase.title} items={config.showcase.items} />
        </div>
      </div>
    </UnifiedLayout>
  );
}
