/**
 * R2 File Upload API - Simplified Version
 * Direct use of Cloudflare Workers R2 binding
 */

import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Direct use of R2 binding
    const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
    if (!R2_BUCKET) {
      return json({ error: "R2 bucket not configured" }, { status: 503 });
    }

    const contentType = request.headers.get("content-type") || "";

    // Handle form data upload
    if (contentType.includes("multipart/form-data")) {
      const formData = await request.formData();
      const file = formData.get("file") as File;

      if (!file || !(file instanceof File)) {
        return json({ error: "No file found" }, { status: 400 });
      }

      // Generate file key
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substring(2, 8);
      const extension = file.name.split(".").pop();
      const key = `uploads/${timestamp}_${randomSuffix}.${extension}`;

      // Upload to R2
      const result = await R2_BUCKET.put(key, file, {
        httpMetadata: {
          contentType: file.type,
        },
        customMetadata: {
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        },
      });

      if (!result) {
        return json({ error: "Upload failed" }, { status: 500 });
      }

      return json({
        success: true,
        file: {
          key,
          url: `/api/r2-download?key=${encodeURIComponent(key)}`,
          size: file.size,
          contentType: file.type,
          etag: result.etag,
        },
      });
    }

    return json({ error: "Unsupported content type" }, { status: 400 });
  } catch (error) {
    console.error("R2 upload error:", error);
    return json(
      {
        error: "Upload failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * Get upload configuration information
 */
export async function loader({ context }: ActionFunctionArgs) {
  const R2_BUCKET = context.cloudflare.env.R2_BUCKET;

  return json({
    configured: !!R2_BUCKET,
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ["image/*", "video/*", "audio/*", "application/pdf"],
    endpoints: {
      upload: "/api/r2-upload",
      download: "/api/r2-download",
      list: "/api/r2-list",
    },
  });
}
