/**
 * 极简登出路由
 * 清除所有认证 Cookie 并重定向
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { clearAuthCookies } from "~/lib/auth/jwt.server";

export async function action() {
  // 清除所有认证 Cookie
  const headers = clearAuthCookies();

  // 重定向到首页
  return redirect("/", { headers });
}

export async function loader() {
  // GET 请求也执行相同的登出逻辑
  const headers = clearAuthCookies();
  return redirect("/", { headers });
}
