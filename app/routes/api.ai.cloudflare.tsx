import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import {
  type CloudflareAIEmbeddingRequest,
  type CloudflareAIImageClassificationRequest,
  type CloudflareAISpeechRequest,
  type CloudflareAITextRequest,
  createCloudflareAIService,
  getAllCloudflareAIModels,
  validateCloudflareAI,
} from "~/lib/ai/cloudflare-ai";
import { respData, respErr } from "~/lib/api/resp";
import { createDb } from "~/lib/db/db";
import { CreditsAmount, CreditsTransType, decreaseCredits } from "~/services/credit";
import { getUserUuid } from "~/services/user";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  const startTime = Date.now();

  try {
    // Validate Cloudflare AI availability
    if (!validateCloudflareAI(context.cloudflare?.env)) {
      return respErr("Cloudflare AI is not available");
    }

    const formData = await request.formData();
    const action = formData.get("action") as string;

    if (!action) {
      return respErr("Action is required");
    }

    // Create Cloudflare AI service
    const aiService = createCloudflareAIService(context.cloudflare!.env.AI);

    // Get user for credit tracking and database
    let userUuid: string | null = null;
    let db: any = null;
    try {
      userUuid = await getUserUuid();
      if (userUuid && context.cloudflare?.env?.DATABASE_URL) {
        db = createDb(context.cloudflare.env.DATABASE_URL);
      }
    } catch (error) {
      console.warn("Failed to get user UUID or database:", error);
    }

    switch (action) {
      case "generate-text": {
        const model = formData.get("model") as string;
        const prompt = formData.get("prompt") as string;
        const maxTokens = parseInt(formData.get("maxTokens") as string) || 2048;
        const temperature = parseFloat(formData.get("temperature") as string) || 0.7;

        if (!model || !prompt) {
          return respErr("Model and prompt are required");
        }

        const request: CloudflareAITextRequest = {
          messages: [{ role: "user", content: prompt }],
          max_tokens: maxTokens,
          temperature: temperature,
        };

        const result = await aiService.generateText(model as any, request);

        // Deduct credits if user is authenticated
        if (userUuid && db) {
          try {
            await decreaseCredits(
              {
                user_uuid: userUuid,
                trans_type: CreditsTransType.AITextGeneration,
                credits: CreditsAmount.AITextGenerationCost,
              },
              db
            );
          } catch (error) {
            console.warn("Failed to deduct credits:", error);
          }
        }

        console.log("Cloudflare AI text generation completed:", {
          model,
          promptLength: prompt.length,
          duration: Date.now() - startTime,
          userUuid,
          timestamp: new Date().toISOString(),
        });

        return respData({
          result,
          model,
          provider: "cloudflare",
          duration: Date.now() - startTime,
        });
      }

      case "generate-embeddings": {
        const model = formData.get("model") as string;
        const text = formData.get("text") as string;

        if (!model || !text) {
          return respErr("Model and text are required");
        }

        const request: CloudflareAIEmbeddingRequest = {
          text: text,
        };

        const result = await aiService.generateEmbeddings(model as any, request);

        // Deduct credits if user is authenticated
        if (userUuid && db) {
          try {
            await decreaseCredits(
              {
                user_uuid: userUuid,
                trans_type: CreditsTransType.AIEmbedding,
                credits: CreditsAmount.AIEmbeddingCost,
              },
              db
            );
          } catch (error) {
            console.warn("Failed to deduct credits:", error);
          }
        }

        console.log("Cloudflare AI embeddings completed:", {
          model,
          textLength: text.length,
          duration: Date.now() - startTime,
          userUuid,
          timestamp: new Date().toISOString(),
        });

        return respData({
          result,
          model,
          provider: "cloudflare",
          duration: Date.now() - startTime,
        });
      }

      case "classify-text": {
        const model = formData.get("model") as string;
        const text = formData.get("text") as string;

        if (!model || !text) {
          return respErr("Model and text are required");
        }

        const result = await aiService.classifyText(model as any, text);

        // Deduct credits if user is authenticated
        if (userUuid && db) {
          try {
            await decreaseCredits(
              {
                user_uuid: userUuid,
                trans_type: CreditsTransType.AITextClassification,
                credits: CreditsAmount.AITextClassificationCost,
              },
              db
            );
          } catch (error) {
            console.warn("Failed to deduct credits:", error);
          }
        }

        console.log("Cloudflare AI text classification completed:", {
          model,
          textLength: text.length,
          duration: Date.now() - startTime,
          userUuid,
          timestamp: new Date().toISOString(),
        });

        return respData({
          result,
          model,
          provider: "cloudflare",
          duration: Date.now() - startTime,
        });
      }

      case "classify-image": {
        const model = formData.get("model") as string;
        const imageFile = formData.get("image") as File;

        if (!model || !imageFile) {
          return respErr("Model and image are required");
        }

        const imageBuffer = await imageFile.arrayBuffer();
        const request: CloudflareAIImageClassificationRequest = {
          image: imageBuffer,
        };

        const result = await aiService.classifyImage(model as any, request);

        // Deduct credits if user is authenticated
        if (userUuid && db) {
          try {
            await decreaseCredits(
              {
                user_uuid: userUuid,
                trans_type: CreditsTransType.AIImageClassification,
                credits: CreditsAmount.AIImageClassificationCost,
              },
              db
            );
          } catch (error) {
            console.warn("Failed to deduct credits:", error);
          }
        }

        console.log("Cloudflare AI image classification completed:", {
          model,
          imageSize: imageBuffer.byteLength,
          duration: Date.now() - startTime,
          userUuid,
          timestamp: new Date().toISOString(),
        });

        return respData({
          result,
          model,
          provider: "cloudflare",
          duration: Date.now() - startTime,
        });
      }

      case "speech-to-text": {
        const model = formData.get("model") as string;
        const audioFile = formData.get("audio") as File;

        if (!model || !audioFile) {
          return respErr("Model and audio are required");
        }

        const audioBuffer = await audioFile.arrayBuffer();
        const request: CloudflareAISpeechRequest = {
          audio: audioBuffer,
        };

        const result = await aiService.speechToText(model as any, request);

        // Deduct credits if user is authenticated
        if (userUuid && db) {
          try {
            await decreaseCredits(
              {
                user_uuid: userUuid,
                trans_type: CreditsTransType.AISpeechToText,
                credits: CreditsAmount.AISpeechToTextCost,
              },
              db
            );
          } catch (error) {
            console.warn("Failed to deduct credits:", error);
          }
        }

        console.log("Cloudflare AI speech-to-text completed:", {
          model,
          audioSize: audioBuffer.byteLength,
          duration: Date.now() - startTime,
          userUuid,
          timestamp: new Date().toISOString(),
        });

        return respData({
          result,
          model,
          provider: "cloudflare",
          duration: Date.now() - startTime,
        });
      }

      case "get-models": {
        const models = getAllCloudflareAIModels();
        return respData({
          models,
          provider: "cloudflare",
        });
      }

      default:
        return respErr(`Unsupported action: ${action}`);
    }
  } catch (error) {
    console.error("Cloudflare AI API error:", error);

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

    return respErr(`Cloudflare AI operation failed: ${errorMessage}`);
  }
}

// Also support GET for health checks and model listing
export async function loader({ context }: ActionFunctionArgs) {
  try {
    // Check Cloudflare AI availability
    const isAvailable = validateCloudflareAI(context.cloudflare?.env);

    if (!isAvailable) {
      return respData({
        status: "unavailable",
        message: "Cloudflare AI binding is not configured",
        models: {},
      });
    }

    const models = getAllCloudflareAIModels();

    return respData({
      status: "available",
      provider: "cloudflare",
      models,
      supportedActions: [
        "generate-text",
        "generate-embeddings",
        "classify-text",
        "classify-image",
        "speech-to-text",
        "get-models",
      ],
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Cloudflare AI health check error:", error);
    return respErr("Health check failed");
  }
}
