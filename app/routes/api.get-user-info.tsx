import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { respData, respErr, respJson } from "~/lib/api/resp";
import { findUserByUuid, getUserCreditBalance, getUserWithRelations } from "~/models/user";
import { getUserUuid } from "~/services/user";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "no auth");
    }

    const user = await findUserByUuid(user_uuid, db);
    if (!user) {
      return respErr("user not exist");
    }

    // Get current credits using the new operation
    user.credits = await getUserCreditBalance(user_uuid, db);

    return respData(user);
  } catch (e) {
    console.log("get user info failed: ", e);
    return respErr("get user info failed");
  }
}

// New enhanced endpoint for getting user with all related data
export async function loader({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "no auth");
    }

    // Get user with all related data (orders, credit history, etc.)
    const userWithRelations = await getUserWithRelations(user_uuid, db);
    if (!userWithRelations) {
      return respErr("user not exist");
    }

    return respData(userWithRelations);
  } catch (e) {
    console.log("get user with relations failed: ", e);
    return respErr("get user with relations failed");
  }
}
