import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData, useSearchParams } from "@remix-run/react";
import { useState } from "react";
import { createDbFromEnv } from "~/lib/db";
import { getUserOrders } from "~/models/order";
import { getUserUuid } from "~/services/user";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Create database connection
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // Get current user UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    // Parse search parameters
    const url = new URL(request.url);
    const searchParams = {
      page: parseInt(url.searchParams.get("page") || "1"),
      limit: parseInt(url.searchParams.get("limit") || "10"),
      sortOrder: (url.searchParams.get("sortOrder") as "asc" | "desc") || "desc",
      status: url.searchParams.get("status")?.split(",") || undefined,
      dateFrom: url.searchParams.get("dateFrom")
        ? new Date(url.searchParams.get("dateFrom")!)
        : undefined,
      dateTo: url.searchParams.get("dateTo")
        ? new Date(url.searchParams.get("dateTo")!)
        : undefined,
    };

    // Get user orders
    const result = await getUserOrders(userUuid, db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      searchParams,
    });
  } catch (error) {
    console.error("Error loading user orders:", error);
    return json(
      {
        success: false,
        error: "Failed to load orders",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export default function ConsoleOrders() {
  const { data, pagination, searchParams } = useLoaderData<typeof loader>();
  const [urlSearchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState({
    status: searchParams?.status?.join(",") || "",
    dateFrom: searchParams?.dateFrom
      ? new Date(searchParams.dateFrom).toISOString().split("T")[0]
      : "",
    dateTo: searchParams?.dateTo ? new Date(searchParams.dateTo).toISOString().split("T")[0] : "",
  });

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    const newParams = new URLSearchParams();

    if (filters.status) newParams.set("status", filters.status);
    if (filters.dateFrom) newParams.set("dateFrom", filters.dateFrom);
    if (filters.dateTo) newParams.set("dateTo", filters.dateTo);

    newParams.set("page", "1"); // Reset to first page
    setSearchParams(newParams);
  };

  const changePage = (page: number) => {
    const newParams = new URLSearchParams(urlSearchParams);
    newParams.set("page", page.toString());
    setSearchParams(newParams);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Failed to load orders</h1>
          <p className="text-gray-600">Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
            <p className="mt-2 text-gray-600">View and track all your orders</p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Filter Orders</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange("status", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="succeeded">Succeeded</option>
                  <option value="failed">Failed</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date To</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange("dateTo", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={applyFilters}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">
                Your Orders ({pagination?.total || 0})
              </h3>
            </div>
          </div>

          {data.length > 0 ? (
            <>
              <div className="divide-y divide-gray-200">
                {data.map((order: any) => (
                  <div key={order.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-lg font-medium text-gray-900">
                              Order #{order.orderNo}
                            </h4>
                            <p className="text-sm text-gray-500">
                              Placed on{" "}
                              {new Date(order.createdAt).toLocaleDateString(undefined, {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                              })}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-medium text-gray-900">
                              ${parseFloat(order.totalAmount).toFixed(2)}
                            </div>
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}
                            >
                              {order.status}
                            </span>
                          </div>
                        </div>

                        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Payment Method:</span>
                            <div className="text-gray-900 capitalize">{order.billingProvider}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Currency:</span>
                            <div className="text-gray-900">{order.currency}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Order ID:</span>
                            <div className="text-gray-900 font-mono text-xs">{order.id}</div>
                          </div>
                        </div>

                        {order.orderDetail && (
                          <div className="mt-4">
                            <span className="font-medium text-gray-700">Details:</span>
                            <div className="text-gray-900 text-sm mt-1">
                              {typeof order.orderDetail === "string"
                                ? order.orderDetail
                                : JSON.stringify(order.orderDetail, null, 2)}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="mt-4 flex justify-end space-x-3">
                      <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View Details
                      </button>
                      {order.status === "succeeded" && (
                        <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                          Download Invoice
                        </button>
                      )}
                      {order.status === "failed" && (
                        <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                          Retry Payment
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                      {pagination.total} results
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => changePage(pagination.page - 1)}
                        disabled={!pagination.hasPrev}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        Previous
                      </button>
                      <span className="px-3 py-1 text-sm text-gray-700">
                        Page {pagination.page} of {pagination.totalPages}
                      </span>
                      <button
                        onClick={() => changePage(pagination.page + 1)}
                        disabled={!pagination.hasNext}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No orders</h3>
              <p className="mt-1 text-sm text-gray-500">You haven't placed any orders yet.</p>
              <div className="mt-6">
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                  Browse Products
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
