/**
 * Multi-Provider Login Page
 * Supports both Google One Tap and Neon Auth simultaneously
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON><PERSON>riangle, ArrowRight } from "lucide-react";
import { GoogleOneTapLogin } from "~/components/auth/google-one-tap";
import { NeonAuthProvider } from "~/components/auth/neon-auth-provider";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getAuthConfig, validateAuthConfig } from "~/lib/auth/auth-config";
import { getUnifiedUser } from "~/lib/auth/unified-middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign In - Choose Your Method - AI SaaS Starter" },
    {
      name: "description",
      content: "Sign in to your account using your preferred authentication method",
    },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Check if user is already authenticated
  const userResult = await getUnifiedUser(request, context.cloudflare?.env);
  if (userResult.success) {
    return redirect("/console");
  }

  // Get authentication configuration
  const authConfig = getAuthConfig(context.cloudflare?.env);
  const validation = validateAuthConfig(authConfig);

  return json({
    authConfig: {
      provider: authConfig.provider,
      primaryProvider: authConfig.primaryProvider,
      googleOneTap: {
        enabled: authConfig.googleOneTap.enabled,
        clientId: authConfig.googleOneTap.clientId,
      },
      neonAuth: {
        enabled: authConfig.neonAuth.enabled,
        projectId: authConfig.neonAuth.projectId,
        publishableKey: authConfig.neonAuth.publishableKey,
      },
    },
    validation,
  });
}

export default function MultiLoginPage() {
  const { authConfig, validation } = useLoaderData<typeof loader>();

  const bothEnabled = authConfig.provider === "both";
  const primaryIsNeon = authConfig.primaryProvider === "neon-auth";

  return (
    <UnifiedLayout
      hero={{
        title: "Welcome Back!",
        description: bothEnabled
          ? "Choose your preferred sign-in method to access your dashboard."
          : "Sign in to access your dashboard and services.",
      }}
    >
      <section className="py-16">
        <div className="max-w-md mx-auto space-y-6">
          {/* Configuration Errors */}
          {!validation.valid && (
            <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-red-800 dark:text-red-200">
                      Configuration Error
                    </h3>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                      <ul className="list-disc list-inside space-y-1">
                        {validation.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Primary Provider (when both enabled) */}
          {bothEnabled && primaryIsNeon && authConfig.neonAuth.enabled && (
            <Card className="shadow-2xl border-2 border-blue-200 bg-blue-50/80 dark:bg-blue-900/20 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-xl font-bold text-blue-800 dark:text-blue-200">
                  🌟 Neon Auth (Recommended)
                </CardTitle>
                <CardDescription className="text-blue-700 dark:text-blue-300">
                  Our new authentication system with enhanced security
                </CardDescription>
              </CardHeader>
              <CardContent>
                <NeonAuthProvider authConfig={authConfig.neonAuth}>
                  <div className="space-y-4">
                    <Link
                      to="/neon-auth/sign-up"
                      className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors text-center"
                    >
                      Create Account with Neon Auth
                    </Link>
                    <Link
                      to="/neon-auth/sign-in"
                      className="block w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-md transition-colors text-center"
                    >
                      Sign In with Neon Auth
                    </Link>
                  </div>
                </NeonAuthProvider>
              </CardContent>
            </Card>
          )}

          {/* Primary Provider (when both enabled and Google is primary) */}
          {bothEnabled && !primaryIsNeon && authConfig.googleOneTap.enabled && (
            <Card className="shadow-2xl border-2 border-green-200 bg-green-50/80 dark:bg-green-900/20 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-xl font-bold text-green-800 dark:text-green-200">
                  🌟 Google One Tap (Recommended)
                </CardTitle>
                <CardDescription className="text-green-700 dark:text-green-300">
                  Quick and secure authentication with your Google account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <GoogleOneTapLogin
                  clientId={authConfig.googleOneTap.clientId!}
                  oneTapEnabled={authConfig.googleOneTap.enabled}
                />
              </CardContent>
            </Card>
          )}

          {/* Alternative Provider (when both enabled) */}
          {bothEnabled && (
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Or use an alternative method:
              </p>
            </div>
          )}

          {/* Secondary Provider - Neon Auth */}
          {bothEnabled && !primaryIsNeon && authConfig.neonAuth.enabled && (
            <Card className="shadow-lg border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-lg font-bold">Neon Auth</CardTitle>
                <CardDescription>Alternative authentication with enhanced features</CardDescription>
              </CardHeader>
              <CardContent>
                <NeonAuthProvider authConfig={authConfig.neonAuth}>
                  <div className="space-y-3">
                    <Link
                      to="/neon-auth/sign-up"
                      className="block w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-center text-sm"
                    >
                      Create Account
                    </Link>
                    <Link
                      to="/neon-auth/sign-in"
                      className="block w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition-colors text-center text-sm"
                    >
                      Sign In
                    </Link>
                  </div>
                </NeonAuthProvider>
              </CardContent>
            </Card>
          )}

          {/* Secondary Provider - Google One Tap */}
          {bothEnabled && primaryIsNeon && authConfig.googleOneTap.enabled && (
            <Card className="shadow-lg border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-lg font-bold">Google One Tap</CardTitle>
                <CardDescription>Quick authentication with your Google account</CardDescription>
              </CardHeader>
              <CardContent>
                <GoogleOneTapLogin
                  clientId={authConfig.googleOneTap.clientId!}
                  oneTapEnabled={authConfig.googleOneTap.enabled}
                />
              </CardContent>
            </Card>
          )}

          {/* Single Provider Mode */}
          {!bothEnabled && (
            <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
                <CardDescription>Access your AI workspace</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {authConfig.neonAuth.enabled ? (
                  <NeonAuthProvider authConfig={authConfig.neonAuth}>
                    <div className="space-y-4">
                      <Link
                        to="/neon-auth/sign-up"
                        className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors text-center"
                      >
                        Create Account
                      </Link>
                      <Link
                        to="/neon-auth/sign-in"
                        className="block w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-md transition-colors text-center"
                      >
                        Sign In
                      </Link>
                    </div>
                  </NeonAuthProvider>
                ) : authConfig.googleOneTap.enabled ? (
                  <GoogleOneTapLogin
                    clientId={authConfig.googleOneTap.clientId!}
                    oneTapEnabled={authConfig.googleOneTap.enabled}
                  />
                ) : (
                  <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
                    <CardContent className="pt-6">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                            No Authentication Configured
                          </h3>
                          <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                            Please configure at least one authentication provider.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terms and Privacy */}
          <div className="text-center text-xs text-gray-500 dark:text-gray-400">
            By signing in, you agree to our{" "}
            <Link
              to="/terms-of-service"
              className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              to="/privacy-policy"
              className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
