import type { MetaFunction } from "@remix-run/cloudflare";
import { Check, Globe, Headphones, Shield, Users, Zap } from "lucide-react";
import CardGrid from "~/components/blocks/card-grid";
import ContentSection from "~/components/blocks/content-section";
import FAQ from "~/components/blocks/faq";
import Pricing from "~/components/blocks/pricing";
import UnifiedLayout from "~/components/layout/unified-layout";

export const meta: MetaFunction = () => {
  return [
    { title: "Pricing - AI SaaS Starter" },
    {
      name: "description",
      content:
        "Choose the perfect plan for your AI-powered SaaS needs. Transparent pricing with no hidden fees.",
    },
  ];
};

const pricingGroups = [
  {
    name: "monthly",
    title: "Monthly",
  },
  {
    name: "yearly",
    title: "Annual",
    label: "Save 20%",
  },
];

const pricingPlans = [
  {
    id: "free",
    name: "Starter",
    description: "Perfect for individuals and small projects",
    price: 0,
    currency: "USD",
    interval: "month" as const,
    features: [
      "100 AI credits per month",
      "Basic text generation",
      "5 image generations",
      "Community support",
      "Standard response time",
      "Basic analytics",
    ],
    buttonText: "Get Started Free",
    buttonVariant: "outline" as const,
    credits: 100,
  },
  {
    id: "pro",
    name: "Professional",
    description: "Best for professionals and growing teams",
    price: 29,
    originalPrice: 39,
    currency: "USD",
    interval: "month" as const,
    features: [
      "5,000 AI credits per month",
      "All AI models access",
      "Unlimited image generation",
      "Priority support",
      "Advanced analytics",
      "API access",
      "Custom integrations",
      "Team collaboration",
    ],
    popular: true,
    buttonText: "Start Pro Trial",
    buttonVariant: "default" as const,
    credits: 5000,
    stripePriceId: "price_pro_monthly",
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "For large organizations with custom needs",
    price: 99,
    currency: "USD",
    interval: "month" as const,
    features: [
      "Unlimited AI credits",
      "Custom AI model training",
      "White-label solution",
      "Dedicated account manager",
      "99.9% SLA guarantee",
      "Custom integrations",
      "Advanced security & compliance",
      "On-premise deployment option",
    ],
    buttonText: "Contact Sales",
    buttonVariant: "outline" as const,
    credits: -1, // Unlimited
  },
];

const features = [
  {
    icon: <Zap className="h-6 w-6" />,
    title: "Lightning Fast",
    description: "Get AI responses in milliseconds with our optimized infrastructure",
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Enterprise Security",
    description: "Bank-level security with SOC 2 compliance and data encryption",
  },
  {
    icon: <Users className="h-6 w-6" />,
    title: "Team Collaboration",
    description: "Work together with your team on AI projects and share resources",
  },
  {
    icon: <Headphones className="h-6 w-6" />,
    title: "24/7 Support",
    description: "Get help when you need it with our dedicated support team",
  },
  {
    icon: <Globe className="h-6 w-6" />,
    title: "Global CDN",
    description: "Fast response times worldwide with our global edge network",
  },
  {
    icon: <Check className="h-6 w-6" />,
    title: "99.9% Uptime",
    description: "Reliable service with industry-leading uptime guarantees",
  },
];

const faqs = [
  {
    question: "What are AI credits?",
    answer:
      "AI credits are used to access our AI services. Different operations consume different amounts of credits. Text generation typically uses 1-5 credits, while image generation uses 10-20 credits.",
  },
  {
    question: "Can I change my plan anytime?",
    answer:
      "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.",
  },
  {
    question: "Do you offer refunds?",
    answer:
      "We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact our support team for a full refund.",
  },
  {
    question: "Is there a free trial?",
    answer:
      "Yes! Our Starter plan is completely free and includes 100 AI credits per month. You can also try our Pro plan free for 14 days.",
  },
];

export default function PricingPage() {
  return (
    <UnifiedLayout
      hero={{
        title: "Simple, Transparent Pricing",
        description:
          "Choose the plan that best fits your needs. All plans include our core features.",
        buttons: [
          {
            text: "Get Started",
            href: "/auth/login",
            variant: "primary",
          },
          {
            text: "Contact Sales",
            href: "/contact",
            variant: "outline",
          },
        ],
      }}
    >
      {/* Pricing Section */}
      <Pricing groups={pricingGroups} plans={pricingPlans} className="bg-background" />

      <ContentSection
        title="Everything you need to succeed"
        description="Our platform includes all the tools and features you need to build amazing AI-powered applications."
        background="muted"
      >
        <CardGrid
          items={features.map((feature) => ({
            icon: feature.icon,
            title: feature.title,
            description: feature.description,
          }))}
          columns={3}
          variant="feature"
        />
      </ContentSection>

      <FAQ
        title="Frequently Asked Questions"
        description="Have questions? We have answers."
        items={faqs}
      />
    </UnifiedLayout>
  );
}
