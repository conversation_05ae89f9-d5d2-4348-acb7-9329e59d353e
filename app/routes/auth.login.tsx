/**
 * Sign In Page
 * Google One Tap authentication interface
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";
import { GoogleOneTapLogin } from "~/components/auth/google-one-tap";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getAuthConfig } from "~/lib/auth/auth-config";
import { getUnifiedUser } from "~/lib/auth/unified-middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign In - AI SaaS Starter" },
    { name: "description", content: "Sign in to your account" },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Check if user is already authenticated using unified auth
  const userResult = await getUnifiedUser(request, context.cloudflare?.env);

  if (userResult.success) {
    // User is already authenticated, redirect to dashboard
    return redirect("/console");
  }

  // Get authentication configuration
  const authConfig = getAuthConfig(context.cloudflare?.env);

  return json({
    googleClientId: authConfig.googleOneTap.clientId,
    oneTapEnabled: authConfig.googleOneTap.enabled,
    neonAuthEnabled: authConfig.neonAuth.enabled,
    authProvider: authConfig.provider,
  });
}

export default function SignInPage() {
  const { googleClientId, oneTapEnabled, neonAuthEnabled, authProvider } =
    useLoaderData<typeof loader>();

  return (
    <UnifiedLayout
      hero={{
        title: "Welcome Back!",
        description: "Sign in to access your dashboard and services.",
      }}
    >
      <section className="py-16">
        <div className="max-w-md mx-auto space-y-6">
          {/* Show Neon Auth option if enabled */}
          {neonAuthEnabled && (
            <Card className="shadow-2xl border-0 bg-blue-50/80 dark:bg-blue-900/20 backdrop-blur-sm border-blue-200">
              <CardHeader className="text-center">
                <CardTitle className="text-xl font-bold text-blue-800 dark:text-blue-200">
                  Neon Auth (Experimental)
                </CardTitle>
                <CardDescription className="text-blue-700 dark:text-blue-300">
                  Try our new authentication system powered by Neon
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link
                  to="/auth/neon-login"
                  className="flex items-center justify-center gap-2 w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors"
                >
                  Sign In with Neon Auth
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </CardContent>
            </Card>
          )}

          {/* Google One Tap Authentication */}
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold">
                {neonAuthEnabled ? "Google One Tap" : "Sign In"}
              </CardTitle>
              <CardDescription>
                {neonAuthEnabled
                  ? "Or continue with Google authentication"
                  : "Access your AI workspace with Google authentication"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {googleClientId && oneTapEnabled ? (
                <GoogleOneTapLogin clientId={googleClientId} oneTapEnabled={oneTapEnabled} />
              ) : (
                <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                          Authentication Not Configured
                        </h3>
                        <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                          Google authentication is not properly configured. Please check your
                          environment variables.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="text-center text-xs text-gray-500 dark:text-gray-400">
                By signing in, you agree to our{" "}
                <Link
                  to="/terms-of-service"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link
                  to="/privacy-policy"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  Privacy Policy
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
