/**
 * 极简登录页面 - Google One Tap 优先，Neon Auth 兜底
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { GoogleOneTap, NeonFallbackButton } from "~/components/auth/google-one-tap";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getUser } from "~/lib/auth/middleware.server";

export const meta: MetaFunction = () => {
  return [{ title: "登录 - AI SaaS Starter" }, { name: "description", content: "登录您的账户" }];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  // 检查用户是否已经登录
  const userResult = await getUser(request);

  if (userResult.success) {
    // 用户已登录，重定向到控制台
    return redirect("/console");
  }

  // 返回 Google Client ID（从环境变量获取）
  return json({
    googleClientId: context.cloudflare?.env?.GOOGLE_CLIENT_ID,
  });
}

export default function SignInPage() {
  const { googleClientId } = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout
      hero={{
        title: "欢迎回来！",
        description: "登录以访问您的控制台和服务。",
      }}
    >
      <section className="py-16">
        <div className="max-w-md mx-auto">
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold">登录</CardTitle>
              <CardDescription>使用 Google 账户快速登录，或使用邮箱登录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Google One Tap - 自动弹窗 */}
              <GoogleOneTap clientId={googleClientId} enabled={!!googleClientId} />

              {/* Neon Auth 备用按钮 */}
              <NeonFallbackButton />

              <div className="text-center text-xs text-gray-500 dark:text-gray-400">
                登录即表示您同意我们的{" "}
                <Link
                  to="/legal/terms"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  服务条款
                </Link>{" "}
                和{" "}
                <Link
                  to="/legal/privacy"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  隐私政策
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
