/**
 * Neon Auth Catch-All Handler Route
 * Handles all Neon Auth authentication flow routes like /neon-auth/sign-in, /neon-auth/sign-up, etc.
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import { NeonAuthProvider, NeonAuthHandler } from "~/components/auth/neon-auth-provider";
import { getAuthConfig, validateAuthConfig } from "~/lib/auth/auth-config";

export const meta: MetaFunction = () => {
  return [
    { title: "Authentication - AI SaaS Starter" },
    { name: "description", content: "Secure authentication powered by Neon Auth" },
  ];
};

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const authConfig = getAuthConfig(context.cloudflare?.env);

  // If Neon Auth is not enabled, redirect to regular login
  if (!authConfig.neonAuth.enabled) {
    return redirect("/auth/login");
  }

  // Validate configuration
  const validation = validateAuthConfig(authConfig);
  if (!validation.valid) {
    console.error("Neon Auth configuration errors:", validation.errors);

    // For critical configuration errors, redirect to fallback
    return redirect("/auth/login");
  }

  // Get the current path for the handler
  const url = new URL(request.url);
  const pathname = url.pathname;

  return json({
    authConfig: {
      projectId: authConfig.neonAuth.projectId,
      publishableKey: authConfig.neonAuth.publishableKey,
      enabled: authConfig.neonAuth.enabled,
    },
    pathname,
    splat: params["*"] || "",
  });
}

export default function NeonAuthHandlerPage() {
  const { authConfig, pathname } = useLoaderData<typeof loader>();

  return (
    <NeonAuthProvider authConfig={authConfig}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <NeonAuthHandler fullPage={true} />
      </div>
    </NeonAuthProvider>
  );
}
