/**
 * Unified Design System for American Users
 *
 * This file contains all the design tokens, patterns, and utilities
 * for creating a consistent, American-friendly user experience.
 */

// Color palette optimized for American users
export const colors = {
  // Primary brand colors - trustworthy blues
  primary: {
    50: "#eff6ff",
    100: "#dbeafe",
    500: "#3b82f6",
    600: "#2563eb",
    700: "#1d4ed8",
    900: "#1e3a8a",
  },

  // Secondary colors - professional grays
  gray: {
    50: "#f9fafb",
    100: "#f3f4f6",
    200: "#e5e7eb",
    300: "#d1d5db",
    400: "#9ca3af",
    500: "#6b7280",
    600: "#4b5563",
    700: "#374151",
    800: "#1f2937",
    900: "#111827",
  },

  // Success, warning, error
  success: "#10b981",
  warning: "#f59e0b",
  error: "#ef4444",
} as const;

// Typography scale for clear hierarchy
export const typography = {
  display: {
    fontSize: "3.5rem",
    fontWeight: "800",
    lineHeight: "1.1",
    letterSpacing: "-0.02em",
  },
  headline: {
    fontSize: "2.5rem",
    fontWeight: "700",
    lineHeight: "1.2",
    letterSpacing: "-0.01em",
  },
  title: {
    fontSize: "1.875rem",
    fontWeight: "600",
    lineHeight: "1.3",
  },
  body: {
    fontSize: "1rem",
    fontWeight: "400",
    lineHeight: "1.6",
  },
  caption: {
    fontSize: "0.875rem",
    fontWeight: "500",
    lineHeight: "1.4",
  },
} as const;

// Spacing scale for consistent layouts
export const spacing = {
  xs: "0.5rem",
  sm: "1rem",
  md: "1.5rem",
  lg: "2rem",
  xl: "3rem",
  "2xl": "4rem",
  "3xl": "6rem",
  "4xl": "8rem",
} as const;

// Border radius for modern feel
export const borderRadius = {
  sm: "0.375rem",
  md: "0.5rem",
  lg: "0.75rem",
  xl: "1rem",
  "2xl": "1.5rem",
  full: "9999px",
} as const;

// Shadow system for depth
export const shadows = {
  sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
  md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06)",
  lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)",
  xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04)",
  "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)",
} as const;

// Animation durations
export const animations = {
  fast: "150ms",
  normal: "300ms",
  slow: "500ms",
} as const;

// Component variants for consistency
// Note: These contain CSS classes as part of the design system
// This is acceptable here as it's a design system configuration
export const componentVariants = {
  button: {
    primary:
      "btn-primary-gradient text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg",
    secondary:
      "bg-white border-2 border-gray-200 text-gray-900 font-semibold px-6 py-3 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:border-gray-300",
    outline:
      "border-2 border-primary-500 text-primary-600 font-semibold px-6 py-3 rounded-lg transition-all duration-300 hover:bg-primary-50",
  },
  card: {
    default: "card-modern p-6",
    elevated: "card-modern p-6 hover:shadow-xl",
    glass: "glass p-6 rounded-lg",
  },
  section: {
    default: "py-16 px-4",
    large: "py-24 px-4",
    hero: "py-32 px-4",
  },
} as const;

// Layout patterns
// Note: Container layouts are part of the design system and widely used
export const layouts = {
  container: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
  containerSmall: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",
  grid: {
    cols1: "grid grid-cols-1 gap-6",
    cols2: "grid grid-cols-1 md:grid-cols-2 gap-6",
    cols3: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
    cols4: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",
  },
  flex: {
    center: "flex items-center justify-center",
    between: "flex items-center justify-between",
    start: "flex items-center justify-start",
    column: "flex flex-col",
  },
} as const;

// American UX patterns
// Note: These contain CSS classes as part of the design system
// This is acceptable here as it defines consistent UX patterns
export const uxPatterns = {
  hero: {
    badge:
      "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800",
    title: "text-display font-bold text-gray-900 dark:text-white mb-6",
    subtitle: "text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl",
    cta: "flex flex-col sm:flex-row gap-4 justify-center items-center",
  },
  section: {
    header: "text-center mb-16",
    title: "text-headline font-bold text-gray-900 dark:text-white mb-4",
    description: "text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",
  },
  card: {
    header: "mb-6",
    title: "text-title font-semibold text-gray-900 dark:text-white mb-2",
    description: "text-gray-600 dark:text-gray-300",
    footer: "mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",
  },
} as const;

// Utility functions
export const utils = {
  cn: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(" ");
  },

  // Generate consistent gradient backgrounds
  gradient: (from: string, to: string): string => {
    return `bg-gradient-to-r from-${from} to-${to}`;
  },

  // Generate consistent hover effects
  hover: (scale = "105"): string => {
    return `transition-all duration-300 hover:scale-${scale} hover:shadow-lg`;
  },

  // Generate consistent focus styles
  focus: (): string => {
    return "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2";
  },
} as const;

// Responsive breakpoints
export const breakpoints = {
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px",
} as const;

// Export everything as a single design system object
export const designSystem = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animations,
  componentVariants,
  layouts,
  uxPatterns,
  utils,
  breakpoints,
} as const;

export default designSystem;
