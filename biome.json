{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["build/**", "public/build/**", "node_modules/**", ".cache/**", "drizzle/**", "*.lock", "wrangler.toml"], "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.json"]}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "formatWithErrors": false}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always"}}, "json": {"formatter": {"trailingCommas": "none"}}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true, "noRedundantAlt": "off", "noSvgWithoutTitle": "off"}, "complexity": {"recommended": true, "noForEach": "off", "useLiteralKeys": "off", "useArrowFunction": "off"}, "correctness": {"recommended": true, "useExhaustiveDependencies": "off"}, "style": {"recommended": true, "useImportType": "error", "noNonNullAssertion": "off", "useSelfClosingElements": "error", "useConst": "error", "noVar": "error", "useTemplate": "off", "noUselessElse": "off", "useNodejsImportProtocol": "off", "useNumberNamespace": "off"}, "suspicious": {"recommended": true, "noExplicitAny": "warn", "noImplicitAnyLet": "off"}, "security": {"recommended": true, "noDangerouslySetInnerHtml": "off"}}}}