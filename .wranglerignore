# Wrangler ignore file
# Files and directories to exclude from Cloudflare Workers deployment

# Reference files (not part of the actual project)
.000/

# Development files
.env
.dev.vars
.env.local
.env.development
.env.test
.env.production

# Build artifacts
node_modules/
.wrangler/
build/
dist/

# Version control
.git/
.github/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test files
test/
*.test.ts
*.test.tsx
*.test.js
*.test.jsx
*.spec.ts
*.spec.tsx
*.spec.js
*.spec.jsx

# Documentation
docs/
README.md
CHANGELOG.md

# Package manager files
yarn.lock
package-lock.json
pnpm-lock.yaml

# TypeScript
tsconfig.json
tsconfig.*.json

# Linting and formatting
.eslintrc*
.prettierrc*
biome.json

# Other config files
vite.config.ts
postcss.config.js
tailwind.config.ts
drizzle.config.ts
components.json
