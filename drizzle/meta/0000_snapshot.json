{"id": "e5f4ba3a-7af9-4f78-80d4-6ea48a38630a", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "picture_url": {"name": "picture_url", "type": "text", "primaryKey": false, "notNull": false}, "is_personal_account": {"name": "is_personal_account", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "primary_owner_user_id": {"name": "primary_owner_user_id", "type": "text", "primaryKey": false, "notNull": true}, "public_data": {"name": "public_data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"accounts_slug_unique": {"name": "accounts_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.accounts_memberships": {"name": "accounts_memberships", "schema": "", "columns": {"account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "account_role": {"name": "account_role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"accounts_memberships_account_id_idx": {"name": "accounts_memberships_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "accounts_memberships_user_id_idx": {"name": "accounts_memberships_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_memberships_account_id_accounts_id_fk": {"name": "accounts_memberships_account_id_accounts_id_fk", "tableFrom": "accounts_memberships", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "accounts_memberships_user_id_users_id_fk": {"name": "accounts_memberships_user_id_users_id_fk", "tableFrom": "accounts_memberships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "accounts_memberships_account_role_roles_name_fk": {"name": "accounts_memberships_account_role_roles_name_fk", "tableFrom": "accounts_memberships", "tableTo": "roles", "columnsFrom": ["account_role"], "columnsTo": ["name"], "onDelete": "no action", "onUpdate": "no action"}, "accounts_memberships_created_by_users_id_fk": {"name": "accounts_memberships_created_by_users_id_fk", "tableFrom": "accounts_memberships", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "accounts_memberships_updated_by_users_id_fk": {"name": "accounts_memberships_updated_by_users_id_fk", "tableFrom": "accounts_memberships", "tableTo": "users", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_memberships_account_id_user_id_pk": {"name": "accounts_memberships_account_id_user_id_pk", "columns": ["account_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.affiliates": {"name": "affiliates", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_uuid": {"name": "user_uuid", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "text", "primaryKey": false, "notNull": true}, "paid_order_no": {"name": "paid_order_no", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "paid_amount": {"name": "paid_amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "reward_percent": {"name": "reward_percent", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "reward_amount": {"name": "reward_amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"affiliates_user_uuid_idx": {"name": "affiliates_user_uuid_idx", "columns": [{"expression": "user_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "affiliates_invited_by_idx": {"name": "affiliates_invited_by_idx", "columns": [{"expression": "invited_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "affiliates_status_idx": {"name": "affiliates_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "user_uuid": {"name": "user_uuid", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"api_keys_api_key_idx": {"name": "api_keys_api_key_idx", "columns": [{"expression": "api_key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_user_uuid_idx": {"name": "api_keys_user_uuid_idx", "columns": [{"expression": "user_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_account_id_idx": {"name": "api_keys_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_status_idx": {"name": "api_keys_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"api_keys_account_id_accounts_id_fk": {"name": "api_keys_account_id_accounts_id_fk", "tableFrom": "api_keys", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_api_key_unique": {"name": "api_keys_api_key_unique", "nullsNotDistinct": false, "columns": ["api_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_customers": {"name": "billing_customers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {"billing_customers_account_id_idx": {"name": "billing_customers_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"billing_customers_account_id_accounts_id_fk": {"name": "billing_customers_account_id_accounts_id_fk", "tableFrom": "billing_customers", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"billing_customers_customer_id_provider_unique": {"name": "billing_customers_customer_id_provider_unique", "nullsNotDistinct": false, "columns": ["customer_id", "provider"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "trans_no": {"name": "trans_no", "type": "text", "primaryKey": false, "notNull": true}, "user_uuid": {"name": "user_uuid", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": false}, "trans_type": {"name": "trans_type", "type": "text", "primaryKey": false, "notNull": true}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "order_no": {"name": "order_no", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"credit_transactions_trans_no_idx": {"name": "credit_transactions_trans_no_idx", "columns": [{"expression": "trans_no", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_user_uuid_idx": {"name": "credit_transactions_user_uuid_idx", "columns": [{"expression": "user_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_account_id_idx": {"name": "credit_transactions_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_trans_type_idx": {"name": "credit_transactions_trans_type_idx", "columns": [{"expression": "trans_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_created_at_idx": {"name": "credit_transactions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_expires_at_idx": {"name": "credit_transactions_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"credit_transactions_account_id_accounts_id_fk": {"name": "credit_transactions_account_id_accounts_id_fk", "tableFrom": "credit_transactions", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"credit_transactions_trans_no_unique": {"name": "credit_transactions_trans_no_unique", "nullsNotDistinct": false, "columns": ["trans_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_uuid": {"name": "user_uuid", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'created'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "invite_token": {"name": "invite_token", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"invitations_account_id_idx": {"name": "invitations_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitations_email_idx": {"name": "invitations_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitations_invite_token_idx": {"name": "invitations_invite_token_idx", "columns": [{"expression": "invite_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitations_expires_at_idx": {"name": "invitations_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitations_account_id_accounts_id_fk": {"name": "invitations_account_id_accounts_id_fk", "tableFrom": "invitations", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitations_role_roles_name_fk": {"name": "invitations_role_roles_name_fk", "tableFrom": "invitations", "tableTo": "roles", "columnsFrom": ["role"], "columnsTo": ["name"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invitations_invite_token_unique": {"name": "invitations_invite_token_unique", "nullsNotDistinct": false, "columns": ["invite_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "notification_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'info'"}, "channel": {"name": "channel", "type": "notification_channel", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'in_app'"}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": false}, "dismissed": {"name": "dismissed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"notifications_account_id_idx": {"name": "notifications_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_user_id_idx": {"name": "notifications_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_type_idx": {"name": "notifications_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_dismissed_idx": {"name": "notifications_dismissed_idx", "columns": [{"expression": "dismissed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_created_at_idx": {"name": "notifications_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_account_id_accounts_id_fk": {"name": "notifications_account_id_accounts_id_fk", "tableFrom": "notifications", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_items": {"name": "order_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "price_amount": {"name": "price_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"order_items_order_id_idx": {"name": "order_items_order_id_idx", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_items_product_id_idx": {"name": "order_items_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"order_items_order_id_orders_id_fk": {"name": "order_items_order_id_orders_id_fk", "tableFrom": "order_items", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "order_no": {"name": "order_no", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "billing_customer_id": {"name": "billing_customer_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_uuid": {"name": "user_uuid", "type": "text", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": true}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "billing_provider": {"name": "billing_provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "provider_order_id": {"name": "provider_order_id", "type": "text", "primaryKey": false, "notNull": false}, "order_detail": {"name": "order_detail", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"orders_order_no_idx": {"name": "orders_order_no_idx", "columns": [{"expression": "order_no", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_account_id_idx": {"name": "orders_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_user_uuid_idx": {"name": "orders_user_uuid_idx", "columns": [{"expression": "user_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_status_idx": {"name": "orders_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_created_at_idx": {"name": "orders_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_account_id_accounts_id_fk": {"name": "orders_account_id_accounts_id_fk", "tableFrom": "orders", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_billing_customer_id_billing_customers_id_fk": {"name": "orders_billing_customer_id_billing_customers_id_fk", "tableFrom": "orders", "tableTo": "billing_customers", "columnsFrom": ["billing_customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_order_no_unique": {"name": "orders_order_no_unique", "nullsNotDistinct": false, "columns": ["order_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "published": {"name": "published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "author_id": {"name": "author_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"posts_author_id_idx": {"name": "posts_author_id_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_published_idx": {"name": "posts_published_idx", "columns": [{"expression": "published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_created_at_idx": {"name": "posts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_author_id_users_id_fk": {"name": "posts_author_id_users_id_fk", "tableFrom": "posts", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "permission": {"name": "permission", "type": "app_permissions", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {"role_permissions_role_idx": {"name": "role_permissions_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"role_permissions_role_roles_name_fk": {"name": "role_permissions_role_roles_name_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role"], "columnsTo": ["name"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"role_permission_unique": {"name": "role_permission_unique", "nullsNotDistinct": false, "columns": ["role", "permission"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"name": {"name": "name", "type": "text", "primaryKey": true, "notNull": true}, "hierarchy_level": {"name": "hierarchy_level", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_items": {"name": "subscription_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "price_amount": {"name": "price_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "interval": {"name": "interval", "type": "text", "primaryKey": false, "notNull": true}, "interval_count": {"name": "interval_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'flat'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscription_items_subscription_id_idx": {"name": "subscription_items_subscription_id_idx", "columns": [{"expression": "subscription_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_items_product_id_idx": {"name": "subscription_items_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subscription_items_subscription_id_subscriptions_id_fk": {"name": "subscription_items_subscription_id_subscriptions_id_fk", "tableFrom": "subscription_items", "tableTo": "subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "billing_customer_id": {"name": "billing_customer_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "subscription_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "billing_provider": {"name": "billing_provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "period_starts_at": {"name": "period_starts_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "period_ends_at": {"name": "period_ends_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "trial_starts_at": {"name": "trial_starts_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscriptions_account_id_idx": {"name": "subscriptions_account_id_idx", "columns": [{"expression": "account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_status_idx": {"name": "subscriptions_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_active_idx": {"name": "subscriptions_active_idx", "columns": [{"expression": "active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_period_ends_at_idx": {"name": "subscriptions_period_ends_at_idx", "columns": [{"expression": "period_ends_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subscriptions_account_id_accounts_id_fk": {"name": "subscriptions_account_id_accounts_id_fk", "tableFrom": "subscriptions", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subscriptions_billing_customer_id_billing_customers_id_fk": {"name": "subscriptions_billing_customer_id_billing_customers_id_fk", "tableFrom": "subscriptions", "tableTo": "billing_customers", "columnsFrom": ["billing_customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "invite_code": {"name": "invite_code", "type": "text", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "text", "primaryKey": false, "notNull": false}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "signin_type": {"name": "signin_type", "type": "text", "primaryKey": false, "notNull": false}, "signin_provider": {"name": "signin_provider", "type": "text", "primaryKey": false, "notNull": false}, "signin_openid": {"name": "signin_openid", "type": "text", "primaryKey": false, "notNull": false}, "signin_ip": {"name": "signin_ip", "type": "text", "primaryKey": false, "notNull": false}, "is_affiliate": {"name": "is_affiliate", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_uuid_idx": {"name": "users_uuid_idx", "columns": [{"expression": "uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_invite_code_idx": {"name": "users_invite_code_idx", "columns": [{"expression": "invite_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_uuid_unique": {"name": "users_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_invite_code_unique": {"name": "users_invite_code_unique", "nullsNotDistinct": false, "columns": ["invite_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.app_permissions": {"name": "app_permissions", "schema": "public", "values": ["roles.manage", "billing.manage", "settings.manage", "members.manage", "invites.manage", "api.manage"]}, "public.billing_provider": {"name": "billing_provider", "schema": "public", "values": ["stripe", "lemon-squeezy", "paddle"]}, "public.notification_channel": {"name": "notification_channel", "schema": "public", "values": ["in_app", "email"]}, "public.notification_type": {"name": "notification_type", "schema": "public", "values": ["info", "warning", "error", "success"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "succeeded", "failed"]}, "public.subscription_status": {"name": "subscription_status", "schema": "public", "values": ["active", "trialing", "past_due", "canceled", "unpaid", "incomplete", "incomplete_expired", "paused"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}