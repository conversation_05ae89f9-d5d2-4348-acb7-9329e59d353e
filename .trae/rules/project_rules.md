# Project Rules

## Package Management
- Use **Yarn** as the package manager for all dependency management
- Always use package manager commands instead of manually editing package.json
- Run `yarn install` for installing dependencies
- Use `yarn add <package>` for adding new dependencies
- Use `yarn remove <package>` for removing dependencies

## Code Standards
- All code, comments, and documentation must be written in **English**
- Use consistent naming conventions (camelCase for variables/functions, PascalCase for components)
- Follow TypeScript best practices and maintain strict type safety
- Use meaningful variable and function names that clearly describe their purpose

## Error Handling
- Handle errors gracefully instead of throwing direct errors when environment variables or dependencies are missing
- Log runtime errors (undefined destructuring, missing references) instead of crashing the application
- Provide fallback mechanisms for missing configurations

## State Management
- Use **Zustand** as the primary state management solution
- Eliminate other state management approaches in favor of unified Zustand-based patterns
- Keep state management simple and predictable

## UI/UX Guidelines
- Prefer subtle animations over excessive ones
- Ensure pricing sections are visually appealing and well-formatted
- Focus on clean, professional design patterns
- Use consistent spacing and typography

## Analytics
- Use **Google Analytics only** - avoid multiple analytics providers
- Keep analytics implementation simple and focused

## Git Workflow
- Write commit messages in English
- Create multiple smaller commits organized by functionality rather than single large commits
- Use descriptive commit messages that explain the "what" and "why"

## CI/CD
- Configure workflows to show warnings for code and format check failures rather than failing the entire pipeline
- Allow compilation and other steps to continue even when non-critical checks fail
- Maintain build stability while providing useful feedback

## Architecture Patterns
- Reference existing Next.js patterns (particularly from shipany-ai-saas project) when implementing new features
- Adapt API structures appropriately when migrating patterns from Next.js to Remix framework
- Maintain consistency with established project patterns

## Development Workflow
- Write tests for new functionality and run them to verify correctness
- Use the codebase-retrieval tool to understand existing code before making changes
- Be conservative with changes and respect existing codebase structure