// Generated by Wrangler by running `wrangler types`

interface Env {
  DATABASE_URL?: string;
  STRIPE_PUBLIC_KEY?: string;
  STRIPE_SECRET_KEY?: string;
  STRIPE_WEBHOOK_SECRET?: string;
  GA_TRACKING_ID?: string;
  WEB_URL?: string;
  PROJECT_NAME?: string;
  GOOGLE_CLIENT_ID?: string;
  ONE_TAP_ENABLED?: string;

  // Neon Auth Configuration
  NEON_AUTH_ENABLED?: string;
  VITE_STACK_PROJECT_ID?: string;
  VITE_STACK_PUBLISHABLE_CLIENT_KEY?: string;
  STACK_SECRET_SERVER_KEY?: string;
  PRIMARY_AUTH_PROVIDER?: string;

  // AI Provider API Keys
  OPENAI_API_KEY?: string;
  DEEPSEEK_API_KEY?: string;
  OPENROUTER_API_KEY?: string;
  SILICONFLOW_API_KEY?: string;
  SILICONFLOW_BASE_URL?: string;
  REPLICATE_API_TOKEN?: string;

  // R2 Storage Configuration
  R2_BUCKET: R2Bucket;
  R2_BUCKET_NAME?: string;
  R2_PUBLIC_URL?: string;
  R2_ACCOUNT_ID?: string;

  // Cloudflare AI Binding
  AI: Ai;
}
